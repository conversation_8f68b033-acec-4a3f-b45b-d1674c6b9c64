import { useState } from "react";
import { Box, Flex, Text, Image } from "@mantine/core";
import Masonry, { ResponsiveMasonry } from "react-responsive-masonry";
import { GalleryViewer } from "../../GalleryViewer";
import { resolveImageUrl } from "../../../utils/imageUrl";
import unavailable from "@assets/unavailable-image.png";

interface ImageGridProps {
	media: { url: string }[];
}

const ImageGrid = ({ media }: ImageGridProps) => {
	const [isGalleryOpen, setGalleryOpen] = useState(false);
	const [initialSlide, setInitialSlide] = useState(0);

	const openGallery = (index: number) => {
		setInitialSlide(index);
		setGalleryOpen(true);
	};

	const visibleImages = 4;
	const remainingImages = media.length - visibleImages;
	const visibleMedia = media.slice(0, visibleImages);

	return (
		<>
			<Box>
				<ResponsiveMasonry columnsCountBreakPoints={{ 350: 1, 750: 2 }}>
					<Masonry>
						{visibleMedia.map((item, index) => (
							<Box
								key={index}
								onClick={() => openGallery(index)}
								style={{
									cursor: "pointer",
									position: "relative",
								}}
							>
								<Image
									src={resolveImageUrl(item.url)}
									alt={`Post image ${index + 1}`}
									fit="cover"
									w="100%"
									h="100%"
									mah={"552px"}
								/>
								{index === visibleImages - 1 &&
									remainingImages > 0 && (
										<Flex
											style={{
												position: "absolute",
												top: 0,
												left: 0,
												right: 0,
												bottom: 0,
												backgroundColor:
													"rgba(0, 0, 0, 0.5)",
												color: "white",
												justifyContent: "center",
												alignItems: "center",
											}}
										>
											<Text size="xl" fw={700}>
												+{remainingImages}
											</Text>
										</Flex>
									)}
							</Box>
						))}
					</Masonry>
				</ResponsiveMasonry>
			</Box>
			<GalleryViewer
				opened={isGalleryOpen}
				onClose={() => setGalleryOpen(false)}
				images={media.map(
					item => resolveImageUrl(item.url) ?? unavailable
				)}
				initial={initialSlide}
			/>
		</>
	);
};

export default ImageGrid;
