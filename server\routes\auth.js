import express from "express";
const router = express.Router();
import User from "../models/User.js";
import { checkLogin } from "../middleware/auth.js";
import crypto from "crypto";
import { forgotPasswordTemplate } from "../utils/emailTemplates.js";
import { APP_CONFIG } from "../config/env.js";
import { generateEncPassword } from "../utils/encryption.js";
import { loginUser } from "../controllers/auth.controller.js";
import { sendEmail } from "../services/emaiService.js";
import { ONBOARDING_STEP, rolesValues } from "../constants/index.js";

router.post("/login", loginUser);

router.post("/logout", (_, res) => {
	res.clearCookie("token", {
		httpOnly: true,
		secure: APP_CONFIG.isProduction || APP_CONFIG.isTest,
		sameSite: "lax",
	});
	res.status(200).json({ message: "Logged out successfully" });
});

router.get("/session", checkLogin, async (req, res) => {
	try {
		const user = req.user;
		const isAdmin =
			user.role === rolesValues.SuperAdmin ||
			user.role === rolesValues.Admin;
		res.json({
			_id: user._id,
			email: user.email,
			firstName: user.firstName,
			secondName: user.secondName,
			mobile: user.mobile,
			role: user.role,
			createdAt: user.createdAt,
			onboardingStep: user.onboardingStep,
			onboardingStepCompleted:
				user.onboardingStep === ONBOARDING_STEP.COMPLETED,
			profileStatus: user.profileStatus,
			canApproveUser: isAdmin ? true : undefined,
			isAdminPanelUser: isAdmin ? user.isAdminPanelUser : undefined,
			isFirstLogin: user.isFirstLogin ? true : undefined,
			image: user.image,
		});
		if (user.isFirstLogin) {
			user.isFirstLogin = false;
			await user.save();
		}
	} catch (error) {
		res.status(400).json({ error: error.message });
	}
});

router.post("/forgot-password", async (req, res) => {
	const { email } = req.body;
	const user = await User.findOne({ email });
	if (!user) {
		return res.status(404).json({ message: "User not found" });
	}

	const token = crypto.randomBytes(20).toString("hex");
	user.resetPasswordToken = token;
	user.resetPasswordExpires = Date.now() + 3600000;
	await user.save();

	const resetLink = `${APP_CONFIG.FRONTEND_URL}/reset-password/${token}`;

	await sendEmail({
		to: email,
		subject: "Reset Your Password",
		html: forgotPasswordTemplate(resetLink),
	});

	res.status(200).json({ message: "Password reset link sent to your email" });
});

router.post("/reset-password/:token", async (req, res) => {
	const user = await User.findOne({
		resetPasswordToken: req.params.token,
		resetPasswordExpires: { $gt: Date.now() },
	});

	if (!user) {
		return res.status(400).json({ message: "Invalid or expired token" });
	}

	const password = req.body.password;
	const hashedPassword = generateEncPassword(password);

	user.password = hashedPassword;
	user.resetPasswordToken = undefined;
	user.resetPasswordExpires = undefined;
	await user.save();

	res.status(200).json({ message: "Password has been reset successfully" });
});

export default router;
