import {
	PUBLIC_CONTACT_PERSON_NAME,
	PUBLIC_EMAIL,
	PUBLIC_NAME,
} from "../constants/index.js";

export const forgotPasswordTemplate = resetLink => `
    <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; padding: 30px; background-color: #f9f9f9; color: #333;">
        <div style="background-color: #ffffff; padding: 40px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);">
        <h1 style="color: #2c3e50; font-size: 24px; margin-bottom: 20px;">Reset Your Password</h1>
        <p style="font-size: 16px; line-height: 1.5;">
            Hi there,
        </p>
        <p style="font-size: 16px; line-height: 1.5;">
            We received a request to reset your password. To proceed, click the button below:
        </p>
        <div style="text-align: center; margin: 30px 0;">
            <a href="${resetLink}" style="padding: 12px 24px; background-color: #007bff; color: #ffffff; text-decoration: none; border-radius: 6px; font-weight: bold;">
            Reset Password
            </a>
        </div>
        <p style="font-size: 14px; color: #777;">
            If you didn’t request a password reset, you can safely ignore this email. This link will expire in 30 minutes.
        </p>
        <p style="font-size: 14px; margin-top: 30px;">
            Best regards,<br/>
            ${PUBLIC_NAME} Team
        </p>
        </div>
    </div>
`;

export const newUserWelcomeTemplate = (
	firstName,
	lastName,
	email,
	password,
	websiteURL
) => `
<div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9; color: #333;">
    <div style="background-color: #ffffff; padding: 30px; border-radius: 8px; box-sizing: border-box; max-width: 100%;">
        <p style="font-size: 16px; line-height: 1.5; margin-bottom: 10px;">
            Hello ${firstName} ${lastName},
        </p>
        <p style="font-size: 16px; line-height: 1.5; margin-bottom: 10px;">
            Trust you are doing well and prospering.
        </p>
        <p style="font-size: 16px; line-height: 1.5; margin-bottom: 10px;">
            Welcome to Gang 360 — community platform for Supermorpheus Gang. It has been designed and built as a friendly, secure and trusted environment for gang members to get to know each other with a 360 degree view of each other’s lives.
        </p>
        <strong style="font-size: 16px; line-height: 1.5; margin-top: 10px;">Video First Profiles</strong>
        <ul style="margin-top: 8px">
            <li style="font-size: 16px; line-height: 1.5; margin-bottom: 10px;">
                To enable the 360 degree view, each member is requested to record three basic videos as their introduction to the gang.
            </li>

            <li style="font-size: 16px; line-height: 1.5; margin-bottom: 10px;">
                Each video will be 5-10 minutes long and will cover one important aspect of their life. These videos will be private and only accessible to the SM community members.
            </li>

            <li style="font-size: 16px; line-height: 1.5; margin-bottom: 10px;">
                ⁠In our experience and understanding, video-based profiles are one of the most natural and authentic ways to describe yourself, to understand other community members, and further build lasting friendships.
            </li>
        </ul>
        <p style="font-size: 16px; line-height: 1.5; margin-bottom: 10px;">
            Please allocate a 20-30 minute slot in your day when you can create your profile and record videos in a relaxed setting. You can also record different videos on different days. If required, you can browse through a few sample profiles and videos to get an idea. <strong>Note: The invite link is valid for 2 weeks only and will be disabled if your account is not created by then.</strong>
        </p>
        <p style="font-size: 16px; line-height: 1.5; margin-bottom: 10px;">
            Your account has been created successfully. Below are your login credentials:
        </p>
        <p style="font-size: 16px; line-height: 1.5; margin-bottom: 10px;">
            <strong>Email:</strong> ${email}<br/>
            <strong>Password:</strong> ${password}<br/>
            <strong>Website URL:</strong> <a href="${websiteURL}" target="_blank">${websiteURL}</a>
        </p>
        <p style="font-size: 16px; line-height: 1.5; margin-bottom: 10px;">
            As next step, please log in using these details and remember to update your password after your first login for security.
        </p>
        <p style="font-size: 16px; line-height: 1.5; margin-bottom: 10px;">
            If you have any questions / concerns please get in touch with ${PUBLIC_CONTACT_PERSON_NAME} - <a href="mailto:${PUBLIC_EMAIL}">${PUBLIC_EMAIL}</a>
        </p>
        <p style="font-size: 16px; line-height: 1.5;">
            Best regards,<br/>
            ${PUBLIC_NAME} Team
        </p>
    </div>
</div>
`;

export const specificVideoProcessedTemplate = (
	firstName,
	lastName,
	videoType,
	nextStepLink
) => `
    <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; padding: 30px; background-color: #f9f9f9; color: #333;">
        <div style="background-color: #ffffff; padding: 40px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);">
            <p style="font-size: 16px; line-height: 1.5;">
                Hi ${firstName} ${lastName},
            </p>
            <p style="font-size: 16px; line-height: 1.5;">
                We're happy to let you know that your ${videoType} video has been successfully processed. You can verify your details. Please ignore this email if already verified.
            </p>
            <div>
                <a href="${nextStepLink}" 
                style="color: #007bff; font-weight: bold; font-size: 16px; line-height: 1.5; margin-top: 10px; text-decoration: none;">
                    Continue
                </a>
                <span style="font-size: 16px; line-height: 1.5; margin-top: 10px;">
                    Building Gang 360 Profile
                </span>
            </div>
            <p style="font-size: 16px; line-height: 1.5;">
                If you have any questions / concerns please get in touch with ${PUBLIC_CONTACT_PERSON_NAME} - <a href="mailto:${PUBLIC_EMAIL}">${PUBLIC_EMAIL}</a>
            </p>
            <p style="font-size: 16px; line-height: 1.5; margin-top: 30px;">
                Best regards,<br/>
                ${PUBLIC_NAME} Team
            </p>
        </div>
    </div>
`;

export const profileStatusUpdateTemplate = (
	userName,
	newStatus,
	profileLink
) => `
    <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; padding: 30px; background-color: #f9f9f9; color: #333;">
        <div style="background-color: #ffffff; padding: 40px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);">
            <h1 style="color: #2c3e50; font-size: 24px; margin-bottom: 20px;">Welcome to the SM 360 community</h1>
            
            <p style="font-size: 16px; line-height: 1.5;">
                Hi ${userName},
            </p>
            
            <p style="font-size: 16px; line-height: 1.5;">
                We wanted to let you know that your profile has been successfully reviewed and published.
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="${profileLink}" style="display: inline-block; padding: 12px 24px; background-color: #007bff; color: #ffffff; text-decoration: none; border-radius: 6px; font-weight: bold; font-size: 16px;">
                    Check Your Profile
                </a>
            </div>
            
            <p style="font-size: 16px; line-height: 1.5;">
                If you have any questions / concerns please get in touch with ${PUBLIC_CONTACT_PERSON_NAME} - <a href="mailto:${PUBLIC_EMAIL}">${PUBLIC_EMAIL}</a>
            </p>
            
            <p style="font-size: 16px; line-height: 1.5; margin-top: 30px;">
                Warm regards,<br/>
                ${PUBLIC_NAME} Team
            </p>
        </div>
    </div>
`;

export const videoProcessingFailedTemplate = (videoType, retryLink) => `
    <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; padding: 30px; background-color: #f9f9f9; color: #333;">
        <div style="background-color: #ffffff; padding: 40px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);">
            <h1 style="color: #e74c3c; font-size: 24px; margin-bottom: 20px;">We Couldn’t Process Your Video This Time</h1>
            
            <p style="font-size: 16px; line-height: 1.5;">
                Hi there,
            </p>
            
            <p style="font-size: 16px; line-height: 1.5;">
                Unfortunately, we weren’t able to finish processing your <strong>${videoType} life</strong> video this time.
            </p>
            
            <p style="font-size: 16px; line-height: 1.5;">
                Don’t worry—you can give it another try by re-uploading and processing the video again.
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="${retryLink}" style="padding: 12px 24px; background-color: #007bff; color: #ffffff; text-decoration: none; border-radius: 6px; font-weight: bold;">
                    Try Again
                </a>
            </div>
            
            <p style="font-size: 14px; color: #777;">
                Still running into problems? Our support team is here to help—just reach out and we’ll get you sorted.
            </p>
            
            <p style="font-size: 14px; margin-top: 30px;">
                Best wishes,<br/>
                ${PUBLIC_NAME} Team
            </p>
        </div>
    </div>
`;

export const userDeletionNotificationTemplate = userName => `
    <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; padding: 30px; background-color: #f9f9f9; color: #333;">
        <div style="background-color: #ffffff; padding: 40px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);">
        <h1 style="color: #2c3e50; font-size: 24px; margin-bottom: 20px;">Account Deletion Notification</h1>
        <p style="font-size: 16px; line-height: 1.5;">
            Hi ${userName || "there"},
        </p>
        <p style="font-size: 16px; line-height: 1.5;">
            This email is to confirm that your account with Gang 360 has been successfully deleted.
        </p>
        <p style="font-size: 16px; line-height: 1.5;">
            We're sorry to see you go. If you have any feedback, we'd love to hear it.
        </p>
        <p style="font-size: 14px; margin-top: 30px;">
            Best regards,<br/>
            ${PUBLIC_NAME} Team
        </p>
        </div>
    </div>
`;

export const maxRetryLimitTemplate = (videoType, userEmail) => `
    <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; padding: 30px; background-color: #f9f9f9; color: #333;">
        <div style="background-color: #ffffff; padding: 40px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);">
        <h1 style="color: #e74c3c; font-size: 24px; margin-bottom: 20px;">Maximum Retry Limit Reached</h1>
        <p style="font-size: 16px; line-height: 1.5;">
            Hi there,
        </p>
        <p style="font-size: 16px; line-height: 1.5;">
            This is to notify you that the user with the email address <strong>${userEmail}</strong> has reached the maximum number of retries for a ${videoType} video.
        </p>
        <p style="font-size: 16px; line-height: 1.5;">
            No further attempts will be allowed for this user until the issue is manually resolved. Please review the user's activity and take the necessary action.
        </p>
        <p style="font-size: 14px; margin-top: 30px;">
            Best regards,<br/>
            ${PUBLIC_NAME} Team
        </p>
        </div>
    </div>
`;

export const feedbackTemplate = (fullName, feedbackData) => {
	let feedbackHtml = "";

	if (feedbackData["basic-details"]) {
		feedbackHtml += `
        <div style="margin-bottom: 20px;">
            <h3 style="color: #e74c3c; font-size: 18px; margin-bottom: 6px;">Basic Details</h3>
            <p style="font-size: 15px; line-height: 1.5; margin: 0; white-space: pre-line;">${feedbackData["basic-details"]}</p>
        </div>
    `;
	}
	if (feedbackData["early-life"]) {
		feedbackHtml += `
        <div style="margin-bottom: 20px;">
            <h3 style="color: #e74c3c; font-size: 18px; margin-bottom: 6px;">Early Life</h3>
            <p style="font-size: 15px; line-height: 1.5; margin: 0; white-space: pre-line;">${feedbackData["early-life"]}</p>
        </div>
    `;
	}
	if (feedbackData["professional-life"]) {
		feedbackHtml += `
        <div style="margin-bottom: 20px;">
            <h3 style="color: #e74c3c; font-size: 18px; margin-bottom: 6px;">Professional Life</h3>
            <p style="font-size: 15px; line-height: 1.5; margin: 0; white-space: pre-line;">${feedbackData["professional-life"]}</p>
        </div>
    `;
	}
	if (feedbackData["current-life"]) {
		feedbackHtml += `
        <div style="margin-bottom: 20px;">
            <h3 style="color: #e74c3c; font-size: 18px; margin-bottom: 6px;">Current Life</h3>
            <p style="font-size: 15px; line-height: 1.5; margin: 0; white-space: pre-line;">${feedbackData["current-life"]}</p>
        </div>
    `;
	}

	return `
<div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9; color: #333;">
    <div style="background-color: #ffffff; padding: 30px; border-radius: 8px; box-sizing: border-box; max-width: 100%;">
        
        <p style="font-size: 16px; line-height: 1.5; margin-bottom: 10px;">
            Hello ${fullName},
        </p>

        <p style="font-size: 16px; line-height: 1.5; margin-bottom: 10px;">
            Thank you for updating your profile. After reviewing, we found some areas that need changes before it can be approved. Please review the feedback below and update your profile accordingly:
        </p>

        ${feedbackHtml || `<p style="font-size: 15px; line-height: 1.5; color: #555;">No specific feedback provided.</p>`}

        <p style="font-size: 16px; line-height: 1.5; margin-bottom: 10px;">
            If you have any questions / concerns please get in touch with ${PUBLIC_CONTACT_PERSON_NAME} - <a href="mailto:${PUBLIC_EMAIL}">${PUBLIC_EMAIL}</a>
        </p>

        <p style="font-size: 16px; line-height: 1.5; margin-top: 30px;">
            Warm regards,<br/>
            ${PUBLIC_NAME} Team
        </p>
    </div>
</div>
    `;
};
