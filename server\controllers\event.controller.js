import {
	EVENT_ATTENDANCE_STATUS,
	EVENT_STATUS,
	rolesVal<PERSON>,
	ProfileStatus,
	POST_APPROVED_STATUS,
} from "../constants/index.js";
import { Event, EventPost, Media } from "../models/Event.js";
import User from "../models/User.js";
import { getSignedUrlForView } from "../services/awsSpace.js";

export const createEvent = async (req, res) => {
	try {
		const { name, description, detail, startDate, endDate } = req.body;
		const userId = req.user._id;

		if (!name || !description || !startDate || !endDate || !detail) {
			return res.status(400).json({
				message:
					"Name, description, details, start date, and end date are required",
			});
		}

		const start = new Date(startDate);
		const end = new Date(endDate);

		if (start >= end) {
			return res
				.status(400)
				.json({ message: "End date must be after start date" });
		}

		const event = new Event({
			name: name.trim(),
			description: description.trim(),
			startDate: start,
			endDate: end,
			detail: detail,
			createdBy: userId,
		});

		await event.save();

		res.status(201).json({
			message: "Event created successfully",
			eventId: event._id,
		});
	} catch (error) {
		console.error("Error creating event:", error);
		res.status(500).json({ message: "Server error" });
	}
};

export const getAllEvents = async (req, res) => {
	try {
		const tab = req.query.tab; // 'live-events' | 'upcoming-events' | 'past-events' | 'draft-events'
		const page = parseInt(req.query.page) || 1;
		const limit = parseInt(req.query.limit) || 12;

		// If a tab is provided and pagination is requested, return a paginated response for that tab only
		if (tab && ("page" in req.query || "limit" in req.query)) {
			const now = new Date();
			const baseFilter = { isDeleted: false };
			let filter = { ...baseFilter };
			let sort = {};

			switch (tab) {
				case "draft-events": {
					// Only non-community members can see drafts
					if (req.user.role === rolesValues.CommunityMember) {
						return res.status(200).json({
							data: [],
							total: 0,
							page: 1,
							hasMore: false,
						});
					}
					filter = { ...filter, status: { $ne: "published" } };
					sort = { updatedAt: -1 };
					break;
				}
				case "upcoming-events": {
					filter = {
						...filter,
						status: "published",
						startDate: { $gt: now },
					};
					sort = { startDate: 1 };
					break;
				}
				case "past-events": {
					filter = {
						...filter,
						status: "published",
						endDate: { $lte: now },
					};
					sort = { endDate: -1 };
					break;
				}
				case "live-events": {
					filter = {
						...filter,
						status: "published",
						startDate: { $lte: now },
						endDate: { $gte: now },
					};
					sort = { startDate: 1 };
					break;
				}
				default: {
					filter = { ...filter, status: "published" };
					sort = { updatedAt: -1 };
				}
			}

			const skip = (page - 1) * limit;
			const [items, total] = await Promise.all([
				Event.find(filter)
					.select(
						"_id name description startDate endDate status thumbnailImage"
					)
					.populate({ path: "thumbnailImage", select: "S3Key -_id" })
					.sort(sort)
					.skip(skip)
					.limit(limit)
					.lean(),
				Event.countDocuments(filter),
			]);

			const data = await Promise.all(
				items.map(async event => {
					if (event.thumbnailImage?.S3Key) {
						const url = await getSignedUrlForView(
							event.thumbnailImage.S3Key
						);
						return { ...event, thumbnailImage: { url } };
					}
					return event;
				})
			);

			return res.status(200).json({
				data,
				total,
				page,
				hasMore: page * limit < total,
			});
		}

		// Fallback: original non-paginated behavior (aggregates all categories)
		const events = await Event.find({ isDeleted: false })
			.select(
				"_id name description startDate endDate status thumbnailImage"
			)
			.populate({ path: "thumbnailImage", select: "S3Key -_id" })
			.lean();

		const eventsWithUrls = await Promise.all(
			events.map(async event => {
				if (event.thumbnailImage?.S3Key) {
					const url = await getSignedUrlForView(
						event.thumbnailImage.S3Key
					);
					return { ...event, thumbnailImage: { url } };
				}
				return event;
			})
		);

		const now = new Date();
		const computeDraft = () =>
			eventsWithUrls.filter(e => e.status !== "published");
		const computeUpcoming = () =>
			eventsWithUrls.filter(
				e => new Date(e.startDate) > now && e.status === "published"
			);
		const computePast = () =>
			eventsWithUrls.filter(
				e => new Date(e.endDate) <= now && e.status === "published"
			);
		const computeLive = () =>
			eventsWithUrls.filter(
				e =>
					new Date(e.startDate) <= now &&
					new Date(e.endDate) >= now &&
					e.status === "published"
			);

		if (tab) {
			const response = {};
			switch (tab) {
				case "draft-events":
					if (req.user.role !== rolesValues.CommunityMember) {
						response.draftEvents = computeDraft();
					}
					break;
				case "upcoming-events":
					response.upcomingEvents = computeUpcoming();
					break;
				case "past-events":
					response.pastEvents = computePast();
					break;
				case "live-events":
					response.liveEvents = computeLive();
					break;
				default:
					response.draftEvents =
						req.user.role !== rolesValues.CommunityMember
							? computeDraft()
							: undefined;
					response.upcomingEvents = computeUpcoming();
					response.pastEvents = computePast();
					response.liveEvents = computeLive();
			}
			return res.status(200).json(response);
		}

		const draftEvents =
			req.user.role !== rolesValues.CommunityMember
				? computeDraft()
				: undefined;
		const upcomingEvents = computeUpcoming();
		const pastEvents = computePast();
		const liveEvents = computeLive();

		return res
			.status(200)
			.json({ draftEvents, upcomingEvents, pastEvents, liveEvents });
	} catch (error) {
		console.error("Error getting events:", error);
		res.status(500).json({ error: "Failed to fetch events" });
	}
};

export const getEventDetails = async (req, res) => {
	try {
		const { eventId } = req.params;
		const event = await Event.findById(eventId)
			.populate([
				{
					path: "createdBy",
					select: "firstName secondName email image",
				},
				{
					path: "creationMedia",
					select: "S3Key isThumbnail",
				},
				{
					path: "thumbnailImage",
					select: "S3Key",
				},
				{
					path: "attendees",
					select: "userId",
				},
			])
			.lean();

		if (!event || event.isDeleted) {
			return res.status(404).json({ message: "Event not found" });
		}

		const creationMediaWithUrls = await Promise.all(
			(event.creationMedia || []).map(async media => {
				const url = await getSignedUrlForView(media.S3Key);
				return {
					isThumbnail: media.isThumbnail,
					url,
					_id: media._id,
				};
			})
		);

		let thumbnailImage = event.thumbnailImage;
		if (thumbnailImage?.S3Key) {
			const url = await getSignedUrlForView(thumbnailImage.S3Key);
			thumbnailImage = { url };
		}

		const now = new Date();
		const eventTypes = {
			isDraft: false,
			isUpcoming: false,
			isLive: false,
			isPast: false,
		};

		if (event.status !== "published") {
			eventTypes.isDraft = true;
		} else if (new Date(event.startDate) > now) {
			eventTypes.isUpcoming = true;
		} else if (new Date(event.endDate) <= now) {
			eventTypes.isPast = true;
		} else if (
			new Date(event.startDate) <= now &&
			new Date(event.endDate) >= now
		) {
			eventTypes.isLive = true;
		}

		const dataToSend = {
			_id: event._id,
			name: event.name,
			description: event.description,
			detail: event.detail,
			startDate: event.startDate,
			endDate: event.endDate,
			createdBy: event.createdBy,
			thumbnailImage,
			creationMedia: creationMediaWithUrls,
			currentUserAttendingStatus: event.attendees.find(
				attendee => String(attendee.userId) === String(req.user._id)
			)?.status,
			status: event.status,
			createdAt: event.createdAt,
			updatedAt: event.updatedAt,
			counts: {
				totalPosts: event.counts.totalPosts,
				approvedPosts: event.counts.approvedPosts,
				nonAttendingCount: event.counts.nonAttendingCount,
				attendeesCount: event.counts.attendeesCount,
				commentsCount: event.counts.commentsCount,
			},
			eventTypes,
		};

		res.status(200).json(dataToSend);
	} catch (error) {
		console.error("Error getting event details:", error);
		res.status(500).json({ message: "Something went wrong" });
	}
};

export const updateEvent = async (req, res) => {
	try {
		const { eventId } = req.params;
		const {
			name,
			description,
			detail,
			startDate,
			endDate,
			creationMedia,
			removedMediaIds,
			existingThumbnailId,
		} = req.body;

		const event = await Event.findById(eventId);
		if (!event || event.isDeleted) {
			return res.status(404).json({ message: "Event not found" });
		}

		if (name) event.name = name.trim();
		if (description) event.description = description.trim();
		if (detail) event.detail = detail;
		if (startDate) event.startDate = new Date(startDate);
		if (endDate) event.endDate = new Date(endDate);

		// Handle removals of existing media (mark as deleted and pull from event)
		if (Array.isArray(removedMediaIds) && removedMediaIds.length > 0) {
			try {
				await Media.updateMany(
					{ _id: { $in: removedMediaIds } },
					{ $set: { isDeleted: true, isThumbnail: false } }
				);
				// Unset thumbnail if it was among removed media
				if (
					event.thumbnailImage &&
					removedMediaIds.includes(String(event.thumbnailImage))
				) {
					event.thumbnailImage = undefined;
				}
				// Remove from creationMedia list
				event.creationMedia = (event.creationMedia || []).filter(
					id => !removedMediaIds.includes(String(id))
				);
			} catch (e) {
				console.error("Failed to remove media:", e);
			}
		}

		let selectedNewThumbnailId = null;

		if (
			creationMedia &&
			Array.isArray(creationMedia) &&
			creationMedia.length > 0
		) {
			// Create Media documents and append their ObjectIds to existing media
			const mediaIds = [];
			for (const mediaData of creationMedia) {
				const isThumbnail = !!mediaData.isThumbnail;

				const media = new Media({
					S3Key: mediaData.S3Key,
					type: mediaData.type,
					isThumbnail,
					source: "creation",
					uploadedBy: req.user._id,
					approved: "approved",
				});

				await media.save();
				mediaIds.push(media._id);
				if (isThumbnail) {
					selectedNewThumbnailId = media._id;
				}
			}
			event.creationMedia = [...(event.creationMedia || []), ...mediaIds];
		}

		// Ensure only one thumbnail (prefer a newly uploaded one if provided)
		try {
			if (selectedNewThumbnailId) {
				await Media.updateMany(
					{ _id: { $in: event.creationMedia } },
					{ $set: { isThumbnail: false } }
				);
				await Media.updateOne(
					{ _id: selectedNewThumbnailId },
					{ $set: { isThumbnail: true, isDeleted: false } }
				);
				event.thumbnailImage = selectedNewThumbnailId;
			} else if (
				existingThumbnailId &&
				Array.isArray(event.creationMedia) &&
				event.creationMedia.some(
					id => String(id) === String(existingThumbnailId)
				)
			) {
				await Media.updateMany(
					{ _id: { $in: event.creationMedia } },
					{ $set: { isThumbnail: false } }
				);
				await Media.updateOne(
					{ _id: existingThumbnailId },
					{ $set: { isThumbnail: true, isDeleted: false } }
				);
				event.thumbnailImage = existingThumbnailId;
			}
		} catch (e) {
			console.error("Failed to update thumbnail:", e);
		}

		await event.save();

		res.status(200).json({ message: "Event updated successfully" });
	} catch (error) {
		console.error("Error updating event:", error);
		res.status(500).json({ message: "Something went wrong" });
	}
};

export const deleteEvent = async (req, res) => {
	try {
		const { eventId } = req.params;
		const event = await Event.findById(eventId);
		if (!event || event.isDeleted) {
			return res.status(404).json({ message: "Event not found" });
		}
		event.isDeleted = true;
		await event.save();
		res.status(200).json({ message: "Event deleted successfully" });
	} catch (error) {
		console.error("Error deleting event:", error);
		res.status(500).json({ message: "Something went wrong" });
	}
};

export const respondToEvent = async (req, res) => {
	try {
		const userId = req.user._id;
		const { eventId } = req.params;
		const { status } = req.body;

		if (
			!status ||
			!Object.values(EVENT_ATTENDANCE_STATUS).includes(status)
		) {
			return res.status(400).json({ message: "Invalid status provided" });
		}

		const event = await Event.findById(eventId);
		if (!event || event.isDeleted) {
			return res.status(404).json({ message: "Event not found" });
		}

		const existingResponse = event.attendees.find(
			attendee => String(attendee.userId) === String(userId)
		);

		if (existingResponse) {
			if (existingResponse.status === status) {
				return res.status(200).json({
					message: "Response is already recorded",
					counts: event.counts,
				});
			}

			if (existingResponse.status === EVENT_ATTENDANCE_STATUS.Attending) {
				event.counts.attendeesCount--;
			} else if (
				existingResponse.status === EVENT_ATTENDANCE_STATUS.NotAttending
			) {
				event.counts.nonAttendingCount--;
			}

			existingResponse.status = status;
		} else {
			event.attendees.push({ userId, status });
		}

		// Increment the new status count
		if (status === EVENT_ATTENDANCE_STATUS.Attending) {
			event.counts.attendeesCount++;
		} else if (status === EVENT_ATTENDANCE_STATUS.NotAttending) {
			event.counts.nonAttendingCount++;
		}

		await event.save();

		res.status(200).json({
			message: "Response recorded successfully",
			counts: event.counts,
		});
	} catch (error) {
		console.log("Error responding to event:", error);
		res.status(500).json({ message: "Something went wrong" });
	}
};

export const publishEvent = async (req, res) => {
	try {
		const { eventId } = req.params;

		const event = await Event.findById(eventId);
		if (!event || event.isDeleted) {
			return res.status(404).json({ message: "Event not found" });
		}

		if (event.status === EVENT_STATUS.Published) {
			return res
				.status(400)
				.json({ message: "Event is already published" });
		}

		event.status = EVENT_STATUS.Published;
		await event.save();
		res.status(200).json({ message: "Event published successfully" });
	} catch (error) {
		console.error("Error in publishing event:", error);
		res.status(500).json({ message: "Something went wrong" });
	}
};

export const getEventUsers = async (req, res) => {
	try {
		const { eventId, type } = req.params;
		if (!eventId) {
			return res.status(400).json({ message: "Event id is required" });
		}
		if (!type) {
			return res.status(400).json({ message: "Type is required" });
		}
		if (!Object.values(EVENT_ATTENDANCE_STATUS).includes(type)) {
			return res.status(400).json({ message: "Invalid type provided" });
		}

		const event = await Event.findById(eventId);
		if (!event || event.isDeleted) {
			return res.status(404).json({ message: "Event not found" });
		}
		const users = await User.find({
			_id: {
				$in: event.attendees
					.filter(attendee => attendee.status === type)
					.map(attendee => attendee.userId),
			},
		}).select("firstName secondName image email");

		res.status(200).json(users);
	} catch (error) {
		console.error("Error fetching event users:", error);
		res.status(500).json({ message: "Something went wrong" });
	}
};

export const getAllApprovedUsers = async (req, res) => {
	try {
		const { searchTerm } = req.query;
		console.log("searchTerm", searchTerm);
		const query = {
			profileStatus: {
				$in: [ProfileStatus.Approved, ProfileStatus.ReApproved],
			},
			displayStatus: true,
		};

		if (searchTerm) {
			const escapedSearchTerm = searchTerm.replace(
				/[.*+?^${}()|[\]\\]/g,
				"\\$&"
			);

			const terms = escapedSearchTerm.split(/\s+/);
			const andConditions = terms.map(term => {
				const regex = new RegExp(term, "i");
				return {
					$or: [
						{ firstName: regex },
						{ secondName: regex },
						{ middleName: regex },
						{ email: regex },
					],
				};
			});

			query.$and = [
				...(query.$or ? [{ $or: query.$or }] : []),
				...andConditions,
			];
			delete query.$or;
		}

		const users = await User.find(query).select(
			"firstName secondName image email"
		);

		res.status(200).json(users);
	} catch (error) {
		console.error("Error fetching event users:", error);
		res.status(500).json({ message: "Something went wrong" });
	}
};

export const createEventPost = async (req, res) => {
	try {
		const { eventId } = req.params;
		const userId = req.user._id;
		const { content, mediaFiles = [], userTags = [] } = req.body;

		const event = await Event.findOne({
			_id: eventId,
			isDeleted: { $ne: true },
		});
		if (!event) {
			return res.status(404).json({ message: "Event not found" });
		}

		const mediaDocs = mediaFiles.map(file => ({
			S3Key: file.S3Key,
			type: file.type,
			uploadedBy: userId,
			source: "community",
			userTags,
		}));

		const savedMedia = mediaDocs.length
			? await Media.insertMany(mediaDocs, { ordered: false })
			: [];

		const mediaIds = savedMedia.map(m => m._id);

		const post = await EventPost.create({
			eventId,
			userId,
			content,
			media: mediaIds,
			userTags,
		});

		event.counts.totalPosts++;
		await event.save();

		return res.status(201).json({
			message: "Event post created successfully",
			postId: post._id,
		});
	} catch (error) {
		console.error("Error creating event post:", error);
		return res.status(500).json({ message: "Something went wrong" });
	}
};

export const getEventPosts = async (req, res) => {
	try {
		const { eventId } = req.params;
		const page = parseInt(req.query.page) || 1;
		const limit = parseInt(req.query.limit) || 6;
		const status = req.query.postsStatus || "approved";

		if (!Object.values(POST_APPROVED_STATUS).includes(status)) {
			console.log(`Invalid Status to get posts from Event: ${eventId} `);
			return res.status(400).json({ message: "Something went wrong" });
		}

		const event = await Event.findOne({
			_id: eventId,
			isDeleted: { $ne: true },
		});

		if (!event) {
			return res.status(404).json({ message: "Event not found" });
		}

		const baseFilter = { isDeleted: false };
		let filter = { ...baseFilter };
		let sort = {
			createdAt: -1,
		};

		if (status === POST_APPROVED_STATUS.Approved) {
			filter = {
				...filter,
				approved: POST_APPROVED_STATUS.Approved,
			};
		} else if (status === POST_APPROVED_STATUS.Pending) {
			filter = {
				...filter,
				approved: { $ne: POST_APPROVED_STATUS.Approved },
			};
		}

		const skip = (page - 1) * limit;

		const [posts, total] = await Promise.all([
			EventPost.find(filter)
				.select("-updatedAt -isDeleted -__v")
				.populate([
					{
						path: "media",
						select: "S3Key -_id",
					},
					{
						path: "userId",
						select: "firstName secondName image email",
					},
				])
				.sort(sort)
				.skip(skip)
				.limit(limit)
				.lean(),
			EventPost.countDocuments(filter),
		]);

		const data = await Promise.all(
			posts.map(async post => {
				if (post.media && post.media.length > 0) {
					post.media = await Promise.all(
						post.media.map(async m => {
							const url = await getSignedUrlForView(m.S3Key);
							return { url };
						})
					);
				}
				return post;
			})
		);

		return res.status(200).json({
			data: data,
			total,
			page,
			hasMore: page * limit < total,
		});
	} catch (error) {
		console.log(error);
		res.status(500).json({ message: "Something went wrong" });
	}
};

export const deleteEventPost = async (req, res) => {
	try {
		const { postId } = req.params;
		const userId = req.user._id;

		const post = await EventPost.findById(postId);
		if (!post) {
			return res.status(404).json({ message: "Post not found" });
		}
		if (post.userId.toString() !== userId.toString()) {
			return res.status(403).json({ message: "Unauthorized" });
		}
		const event = await Event.findById(post.eventId);

		if (post.approved === POST_APPROVED_STATUS.Approved) {
			event.counts.approvedPosts--;
		}
		event.counts.totalPosts--;

		await event.save();

		post.isDeleted = true;
		await post.save();
		return res.status(200).json({ message: "Post deleted successfully" });
	} catch (error) {
		console.log(`Error deleting event post: ${error}`);
		res.status(500).json({ message: "Something went wrong" });
	}
};
