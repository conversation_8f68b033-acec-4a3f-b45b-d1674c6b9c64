import {
	Table,
	Badge,
	Tooltip,
	Text,
	Group,
	Indicator,
	ActionIcon,
	Menu,
} from "@mantine/core";
import {
	IconDotsVertical,
	IconMail,
	IconMailOpened,
	IconPencil,
	IconTrash,
	IconUser,
} from "@tabler/icons-react";
import {
	ONBOARDING_STEP,
	ONBOARDING_STEP_MESSAGE,
	profileStatusColorMap,
	ProfileStatusMapping,
	roleLabels,
	rolesLabelMap,
} from "../../constants";
import type { pendingProfilesDataType, UserCreation } from "../../types";
import { useNavigate } from "react-router-dom";
import { getProfileStatus } from "../../utils";
import openCustomModal from "../modals/CustomModal";
import EllipsisCell from "../EllipsisCell";
import { ClipboardCheck } from "lucide-react";

interface UserTableprops {
	users: pendingProfilesDataType[];
	onDelete?: (userId: string) => void;
	onEdit?: (user: UserCreation) => void;
	openFeedbackModal?: (user: UserCreation) => void;
}

const UserTable = ({
	users,
	openFeedbackModal,
	onDelete,
	onEdit,
}: UserTableprops) => {
	const navigate = useNavigate();

	const handleUserView = (userId: string) => () => {
		navigate(`/pending-profiles/user/${userId}/basic-details`);
	};

	return (
		<Table highlightOnHover withTableBorder striped verticalSpacing={"sm"}>
			<Table.Thead>
				<Table.Tr>
					<Table.Th>First Name</Table.Th>
					<Table.Th>Last Name</Table.Th>
					<Table.Th>Email</Table.Th>
					<Table.Th>Mobile</Table.Th>
					<Table.Th>Role</Table.Th>
					<Table.Th>Status</Table.Th>
					<Table.Th>Actions</Table.Th>
				</Table.Tr>
			</Table.Thead>
			<Table.Tbody>
				{users.length > 0 ? (
					users.map(user => (
						<Table.Tr key={user._id}>
							<Table.Td>
								<EllipsisCell
									value={user.firstName}
									maxWidth={150}
								/>
							</Table.Td>
							<Table.Td>
								<EllipsisCell
									value={user.secondName}
									maxWidth={150}
								/>
							</Table.Td>
							<Table.Td>
								<EllipsisCell
									value={user.email}
									maxWidth={180}
								/>
							</Table.Td>
							<Table.Td>{user.mobile}</Table.Td>
							<Table.Td>
								{
									rolesLabelMap[
										roleLabels[
											user.role as keyof typeof roleLabels
										]
									]
								}
							</Table.Td>
							<Table.Td>
								{(() => {
									const profileStatusText: string =
										getProfileStatus(user.profileStatus);

									const profileStatusBadgeColor =
										profileStatusColorMap[
											user.profileStatus
										];

									const profileStatusTooltipLabel =
										user.onboardingStep <
										ONBOARDING_STEP.WAIT_FOR_APPROVAL ? (
											<Text
												size="xs"
												maw={200}
												style={{
													whiteSpace: "wrap",
												}}
											>
												{user.resendToOnboarding
													? "Resend to onboarding"
													: ONBOARDING_STEP_MESSAGE[
															user.onboardingStep
														]}
											</Text>
										) : null;

									const unreadCount = user.unreadFeedbacks;

									return (
										<Group gap={12}>
											<Tooltip
												label={
													profileStatusTooltipLabel
												}
												hidden={
													profileStatusTooltipLabel ===
													null
												}
												withArrow
											>
												<Badge
													color={
														profileStatusBadgeColor
													}
													size="sm"
													radius="sm"
													style={{
														textTransform:
															"capitalize",
													}}
												>
													{profileStatusText}
												</Badge>
											</Tooltip>
											{user.actionCompleted ? (
												<Tooltip
													label={`
														User has completed all actions for all feedbacks.
													`}
												>
													<ActionIcon
														variant="subtle"
														onClick={() =>
															openFeedbackModal?.(
																user
															)
														}
													>
														<ClipboardCheck
															size={16}
															color="var(--mantine-color-green-5)"
														/>
													</ActionIcon>
												</Tooltip>
											) : (
												unreadCount != null &&
												(unreadCount > 0 ? (
													<Tooltip
														label={`${unreadCount === 1 ? "feedback" : `feedbacks`} not yet checked by the user.`}
													>
														<Indicator
															zIndex={1}
															autoContrast
															label={
																unreadCount > 9
																	? "9+"
																	: unreadCount.toString()
															}
															inline={true}
															position="top-end"
															size={14}
															offset={1}
														>
															<ActionIcon
																variant="subtle"
																onClick={() =>
																	openFeedbackModal?.(
																		user
																	)
																}
															>
																<IconMail
																	size={18}
																/>
															</ActionIcon>
														</Indicator>
													</Tooltip>
												) : (
													<Tooltip
														label={`
														All feedbacks have been checked by the user.
													`}
													>
														<ActionIcon
															variant="subtle"
															onClick={() =>
																openFeedbackModal?.(
																	user
																)
															}
														>
															<IconMailOpened
																size={16}
																color="var(--mantine-color-green-5)"
															/>
														</ActionIcon>
													</Tooltip>
												))
											)}
										</Group>
									);
								})()}
							</Table.Td>

							<Table.Td align="center">
								<Menu
									position="bottom-end"
									shadow="sm"
									styles={{
										dropdown: {
											minWidth: "140px",
											borderRadius:
												"var(--mantine-radius-md)",
										},
										item: {
											padding: "0.25rem",
											color: "var(--mantine-color-gray-8)",
										},
									}}
								>
									<Menu.Target>
										<ActionIcon
											variant="transparent"
											c={"black"}
										>
											<IconDotsVertical size={16} />
										</ActionIcon>
									</Menu.Target>

									<Menu.Dropdown>
										{user.profileStatus !==
											ProfileStatusMapping.Onboarding && (
											<>
												<Menu.Item
													onClick={handleUserView(
														user._id
													)}
												>
													<Group gap={6}>
														<IconUser size={14} />
														View
													</Group>
												</Menu.Item>
												<Menu.Divider />
											</>
										)}
										<Menu.Item
											onClick={() => onEdit?.(user)}
										>
											<Group gap={6}>
												<IconPencil size={14} />
												Edit
											</Group>
										</Menu.Item>
										<Menu.Divider />
										<Menu.Item
											onClick={() =>
												openCustomModal({
													title: "Delete User",
													description:
														"Are you sure you want to delete this user?",
													confirmCallback: () =>
														onDelete?.(user._id),
												})
											}
										>
											<Group gap={6}>
												<IconTrash size={14} />
												Delete
											</Group>
										</Menu.Item>
									</Menu.Dropdown>
								</Menu>
							</Table.Td>
						</Table.Tr>
					))
				) : (
					<Table.Tr>
						<Table.Td colSpan={8} style={{ textAlign: "center" }}>
							No Users Found.
						</Table.Td>
					</Table.Tr>
				)}
			</Table.Tbody>
		</Table>
	);
};

export default UserTable;
