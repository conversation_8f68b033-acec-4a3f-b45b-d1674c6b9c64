import { useIntersection } from "@mantine/hooks";
import { notifications } from "@mantine/notifications";
import { IconX } from "@tabler/icons-react";
import { isAxiosError } from "axios";
import { useCallback, useEffect, useRef, useState } from "react";
import apiClient from "../../../config/axios";
import { POST_APPROVED_STATUS } from "../../../constants";
import {
	type PostDataType,
	type EventsPostResponseDataType,
	type PostApprovedStatus,
} from "../../../types";
import { Paper } from "@mantine/core";
import PostCard from "./PostCard";

type EventsPostsProps = {
	eventId: string;
};

const LIMIT = 6;
const COOLDOWN_MS = 600;

const EventPosts = (props: EventsPostsProps) => {
	const { eventId } = props;

	const [posts, setPosts] = useState<PostDataType[]>([]);
	const [postsStatus, setPostStatus] =
		useState<PostApprovedStatus>("pending");

	const [page, setPages] = useState<number>(1);
	const [loading, setLoading] = useState<boolean>(false);
	const [loadingMore, setLoadingMore] = useState<boolean>(false);
	const [hasMore, setHasMore] = useState<boolean>(true);

	const fetchEventPosts = useCallback(async () => {
		try {
			setLoading(true);
			const res = await apiClient.get<EventsPostResponseDataType>(
				`/api/events/event-posts/${eventId}/feed`,
				{
					params: {
						page: 1,
						limit: LIMIT,
						postsStatus: postsStatus,
					},
				}
			);
			const payload = res.data;
			setPosts(payload.data ?? []);
			setHasMore(payload.hasMore ?? false);
		} catch (error) {
			if (isAxiosError(error)) {
				notifications.show({
					title: "Failed",
					message:
						error.response?.data?.message ??
						error.message ??
						"Failed to fetch Event Posts",
					color: "red",
					icon: <IconX />,
				});
			} else {
				notifications.show({
					title: "Failed",
					message: "Failed to fetch Event Posts",
					color: "red",
					icon: <IconX />,
				});
			}
		} finally {
			setLoading(false);
		}
	}, [eventId, postsStatus]);

	const deletePost = useCallback(async (postId: string) => {
		try {
			await apiClient.delete(
				`/api/events/event-post/delete-post/${postId}`
			);
			setPosts(prev => prev.filter(post => post._id !== postId));
		} catch (error) {
			if (isAxiosError(error)) {
				notifications.show({
					title: "Failed",
					message:
						error.response?.data?.message ??
						error.message ??
						"Failed to delete post",
					color: "red",
					icon: <IconX />,
				});
			} else {
				notifications.show({
					title: "Failed",
					message: "Failed to delete post",
					color: "red",
					icon: <IconX />,
				});
			}
		}
	}, []);

	useEffect(() => {
		fetchEventPosts();
	}, [fetchEventPosts]);

	return (
		<>
			<Paper>
				{posts.map(post => {
					return (
						<>
							<PostCard post={post} deletePost={deletePost} />
						</>
					);
				})}
			</Paper>
		</>
	);
};

export default EventPosts;
