import {
	<PERSON>ack,
	ThemeIcon,
	<PERSON>lex,
	Button,
	Text,
	Group,
	Divider,
	Paper,
} from "@mantine/core";
import {
	IconMapPin,
	IconHome,
	IconSchool,
	IconTags,
	IconInfoCircle,
} from "@tabler/icons-react";
import React, { useEffect, useState } from "react";
import apiClient from "../../config/axios";
import type { DiffChange, EarlyLifeDataType } from "../../types";
import VideoPreviewAndUpload from "../VideoPreviewAndUpload";
import FullScreenLoader from "../FullScreenLoader";
import { getChangedValue, getDiff, getDiffStyle } from "../../utils/deep-diff";
import DiffList from "./common/DiffList";
import LifeTags from "./common/LifeTags";
import RenderDiff from "./common/RenderDiff";
import DiffCount from "./common/DiffCount";
import { Edit } from "lucide-react";

interface EarlyLifePreviewProps {
	showEdit?: boolean;
	setEditing?: (value: boolean) => void;
	lifeData?: EarlyLifeDataType;
	updatedLifeData?: EarlyLifeDataType;
	userId?: string;
}

interface DiffFieldProps {
	icon: React.ReactNode;
	iconColor: string;
	label: string;
	path: (string | number)[];
	value: string | null | undefined;
	diffChanges: DiffChange[] | null;
}

function DiffField({
	icon,
	iconColor,
	label,
	path,
	value,
	diffChanges,
}: DiffFieldProps) {
	const diff = getChangedValue(path, diffChanges);

	return (
		<Group gap="xs">
			<ThemeIcon variant="light" color={iconColor}>
				{icon}
			</ThemeIcon>
			<Stack gap={-4}>
				<Text c="dimmed" size="xs">
					{label}:
				</Text>
				{diff ? (
					<RenderDiff elementType="text" {...diff} />
				) : (
					<RenderDiff
						elementType="text"
						oldValue={value ?? "N/A"}
						newValue={null}
						type="unchanged"
					/>
				)}
			</Stack>
		</Group>
	);
}
const EarlyLifePreview: React.FC<EarlyLifePreviewProps> = ({
	showEdit,
	setEditing,
	lifeData,
	updatedLifeData,
	userId,
}) => {
	const [earlyLife, setEarlyLife] = useState<EarlyLifeDataType | null>(null);
	const [diffChanges, setDiffChanges] = useState<DiffChange[] | null>(null);
	const [totalChangesCount, setTotoalChangesCount] = useState<number>(0);
	const fetchData = async () => {
		try {
			const response = await apiClient.get<EarlyLifeDataType>(
				"/api/lifeData/earlyLife"
			);
			setEarlyLife(response.data);
		} catch (err) {
			console.error(`Error fetching early life data: ${err}`);
		}
	};
	useEffect(() => {
		if (!userId && !lifeData) {
			fetchData();
		} else if (lifeData) {
			setEarlyLife(lifeData);
		}
	}, [lifeData, userId]);

	useEffect(() => {
		if (lifeData && updatedLifeData) {
			const deepDiff = getDiff({
				originalData: lifeData,
				updatedData: updatedLifeData,
				ignoredKeys: ["videoUrl"],
			});

			if (deepDiff && deepDiff.changes.length > 0) {
				setDiffChanges(deepDiff.changes);
				setTotoalChangesCount(deepDiff.total);
			} else {
				setDiffChanges(null);
			}
		}
	}, [lifeData, updatedLifeData]);

	if (!earlyLife) return <FullScreenLoader />;

	return (
		<Paper p="md">
			{showEdit && (
				<Flex justify="space-between">
					{totalChangesCount > 0 && (
						<DiffCount totalChanges={totalChangesCount} />
					)}
					<Button
						onClick={() => setEditing?.(true)}
						mb={16}
						ml={"auto"}
						leftSection={<Edit size={16} />}
					>
						Edit
					</Button>
				</Flex>
			)}
			<VideoPreviewAndUpload
				editing={true}
				videoPreviewUrl={earlyLife.videoUrl}
				videoType="EarlyLife"
				userId={userId}
			/>

			<Stack>
				<Flex gap="xs" align="flex-start">
					<Group>
						<ThemeIcon variant="light" color="blue">
							<IconInfoCircle size={16} />
						</ThemeIcon>
					</Group>
					<Stack gap={0} justify="flex-start">
						<Text c="dimmed" size="xs">
							Summary:
						</Text>
						{(() => {
							const diff = getChangedValue(
								["earlyLifeSummary"],
								diffChanges
							);
							return diff ? (
								<RenderDiff elementType="text" {...diff} />
							) : (
								<RenderDiff
									elementType="text"
									oldValue={earlyLife.earlyLifeSummary}
									newValue={null}
									type={"unchanged"}
									size="sm"
								/>
							);
						})()}
					</Stack>
				</Flex>

				<Divider label="Birth & Hometown" labelPosition="center" />
				<DiffField
					icon={<IconMapPin size={16} />}
					iconColor="blue"
					label="Birth City"
					path={["birthCity"]}
					value={earlyLife.birthCity}
					diffChanges={diffChanges}
				/>

				<DiffField
					icon={<IconHome size={16} />}
					iconColor="green"
					label="Hometown City"
					path={["hometownCity"]}
					value={earlyLife.hometownCity}
					diffChanges={diffChanges}
				/>

				<DiffList
					label="Schools"
					icon={
						<ThemeIcon variant="light" color="blue">
							<IconSchool
								size={16}
								color="var(--mantine-color-blue-6)"
							/>
						</ThemeIcon>
					}
					data={earlyLife.schools}
					diffChanges={diffChanges ?? null}
					pathKey="schools"
					getStyle={getDiffStyle}
					fields={[
						{ key: "name", props: { size: "sm" } },
						{ key: "location", props: { size: "sm", c: "dimmed" } },
					]}
				/>

				<DiffList
					label="Universities"
					icon={
						<ThemeIcon variant="light" color="violet">
							<IconSchool
								size={16}
								color="var(--mantine-color-violet-6)"
							/>
						</ThemeIcon>
					}
					data={earlyLife.universities}
					diffChanges={diffChanges ?? null}
					pathKey="universities"
					getStyle={getDiffStyle}
					fields={[
						{ key: "name", props: { size: "sm" } },
						{ key: "location", props: { size: "sm", c: "dimmed" } },
						{ key: "course", props: { size: "xs", c: "dimmed" } },
					]}
				/>

				<LifeTags
					tags={earlyLife.earlyLifeTags}
					diffChanges={diffChanges}
					diffPathPrefix="earlyLifeTags"
					icon={
						<ThemeIcon variant="light" color="yellow" mt={2}>
							<IconTags size={16} />
						</ThemeIcon>
					}
					dividerLabelProps={{
						label: "Tags",
						labelPosition: "center",
					}}
				/>
			</Stack>
		</Paper>
	);
};

export default EarlyLifePreview;
