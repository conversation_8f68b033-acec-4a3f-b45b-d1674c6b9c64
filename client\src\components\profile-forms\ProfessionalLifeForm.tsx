import {
	TextInput,
	Paper,
	Title,
	Button,
	Stack,
	Group,
	ActionIcon,
	Text,
	Flex,
	Textarea,
} from "@mantine/core";
import { TagsInput } from "@mantine/core";
import React, { useEffect, useCallback, useState } from "react";
import { IconTrash, IconPlus, IconX } from "@tabler/icons-react";
import apiClient from "../../config/axios";
import { notifications } from "@mantine/notifications";
import { isAxiosError } from "axios";
import VideoPreviewAndUpload from "../VideoPreviewAndUpload";
import type { ProfessionalLifeDataType } from "../../types";
import { useForm } from "@mantine/form";
import openCustomModal from "../modals/CustomModal";
import FullScreenLoader from "../FullScreenLoader";
import { useAuth } from "../../contexts/AuthContext";

interface ProfessionalLifeProps {
	onFormSuccess?: () => void;
	isEditable?: boolean;
	lifeData: ProfessionalLifeDataType | null;
	setHasUnsavedChanges?: (hasChanges: boolean) => void;
	setResetForm?: (resetFunc: () => void) => void;
	setEditing?: (value: boolean) => void;
	editing?: boolean;
	fetchProfile?: () => void;
	userId?: string;
	onboardingStepCompleted?: boolean;
}

const ProfessionalLifeForm: React.FC<ProfessionalLifeProps> = ({
	onFormSuccess,
	lifeData,
	isEditable = true,
	setHasUnsavedChanges,
	setResetForm,
	setEditing,
	editing,
	fetchProfile,
	userId,
	onboardingStepCompleted,
}) => {
	const { user, fetchUser } = useAuth();
	const [loading, setLoading] = useState<boolean>(false);

	const [firstJobRolesError, setFirstJobRolesError] = useState<string | null>(
		null
	);
	const [subsequentJobsRolesError, setSubsequentJobsRolesError] =
		useState<Record<number, string | null> | null>(null);
	const [professionalLifeTagsError, setProfessionalLifeTagsError] = useState<
		string | null
	>(null);

	const form = useForm<ProfessionalLifeDataType>({
		initialValues: {
			firstJob: { companyName: "", roles: [""] },
			subsequentJobs: [],
			professionalLifeTags: [],
			videoUrl: "",
			professionalLifeSummary: "",
		},
		validateInputOnChange: true,
		validateInputOnBlur: true,
		validate: {
			professionalLifeSummary: (value: string) => {
				const trimmed = (value || "").trim();
				if (!trimmed) return "Early Life Summary is required";
				const wordCount = trimmed.split(/\s+/).filter(Boolean).length;
				return wordCount <= 100
					? null
					: "Professional Life Summary must not exceed 100 words";
			},
		},
		transformValues: values => ({
			...values,
			firstJob: {
				companyName: values.firstJob?.companyName?.trim() || "",
				roles: Array.isArray(values.firstJob?.roles)
					? values.firstJob.roles.map(role => role?.trim() || "")
					: [],
				_id: values.firstJob._id,
			},
			subsequentJobs: Array.isArray(values.subsequentJobs)
				? values.subsequentJobs.map(job => ({
						companyName: job?.companyName?.trim() || "",
						roles: Array.isArray(job?.roles)
							? job.roles.map(role => role?.trim() || "")
							: [],
						_id: job._id,
					}))
				: [],
			professionalLifeTags: Array.isArray(values.professionalLifeTags)
				? values.professionalLifeTags.map(tag => tag?.trim() || "")
				: [],
			professionalLifeSummary:
				values.professionalLifeSummary?.trim() || "",
		}),
	});

	const fetchData = useCallback(async () => {
		try {
			setLoading(true);
			const response = await apiClient.get<ProfessionalLifeDataType>(
				"/api/lifeData/professionalLife"
			);
			const data = response.data;

			const fetchedData = {
				...data,
				firstJob: {
					...(data.firstJob || { companyName: "", roles: [] }),
					roles:
						(data.firstJob?.roles?.length ?? 0) > 0
							? data.firstJob.roles
							: isEditable
								? []
								: [],
				},
				subsequentJobs:
					(data.subsequentJobs?.length ?? 0) > 0
						? data.subsequentJobs
						: isEditable
							? [{ companyName: "", roles: [] }]
							: [],
			};
			form.setValues(fetchedData);
			form.setInitialValues(fetchedData);
			setLoading(false);
		} catch (err) {
			console.error(`Error fetching professional life data: ${err}`);
		}
	}, [isEditable]);

	useEffect(() => {
		if (lifeData) {
			const updatedLifeData = {
				...lifeData,
				firstJob: {
					...(lifeData.firstJob || { companyName: "" }),
					roles:
						(lifeData.firstJob?.roles?.length ?? 0) > 0
							? lifeData.firstJob.roles
							: isEditable
								? []
								: [],
				},
				subsequentJobs:
					(lifeData.subsequentJobs?.length ?? 0) > 0
						? lifeData.subsequentJobs
						: isEditable
							? [{ companyName: "", roles: [] }]
							: [],
			};
			form.setValues(updatedLifeData);
			form.setInitialValues(updatedLifeData);
		} else {
			fetchData();
		}
	}, [lifeData, isEditable, fetchData]);

	const resetForm = useCallback(() => {
		form.reset();
	}, [form]);

	useEffect(() => {
		setResetForm?.(resetForm);
	}, [resetForm]);

	useEffect(() => {
		setHasUnsavedChanges?.(form.isDirty());
	}, [form.values]);

	const handleTrimBlur = useCallback(
		(fieldName: string) =>
			(e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
				form.setFieldValue(fieldName, e.target.value.trim());
			},
		[form]
	);

	const handleTagsChange = useCallback(
		(fieldName: string) => (tags: string[]) => {
			const trimmed = tags
				.map(tag => tag.trim())
				.filter(tag => tag.length > 0);

			form.setFieldValue(fieldName, trimmed);
		},
		[form]
	);

	const handleTagsKeyDown = (
		event: React.KeyboardEvent<HTMLInputElement>
	) => {
		const targetValue = (event.target as HTMLInputElement).value.trim();

		if (
			event.key === "Enter" &&
			form.values.professionalLifeTags.length >= 10 &&
			targetValue
		) {
			setProfessionalLifeTagsError("You can only add up to 10 tags");
			event.preventDefault();
		} else {
			setProfessionalLifeTagsError(null);
		}
	};

	const handleSubmit = async (values: ProfessionalLifeDataType) => {
		if (firstJobRolesError || subsequentJobsRolesError) {
			if (
				subsequentJobsRolesError &&
				Object.values(subsequentJobsRolesError).some(error => error)
			) {
				notifications.show({
					icon: <IconX />,
					title: "Validation Error",
					message: "Duplicate roles are not allowed",
					color: "red",
				});
				return;
			}
		}

		if (professionalLifeTagsError) {
			notifications.show({
				icon: <IconX />,
				title: "Validation Error",
				message: professionalLifeTagsError,
				color: "red",
			});
			return;
		}

		if (!form.isDirty() && user && user.onboardingStepCompleted) {
			notifications.show({
				title: "You’re all set",
				message: "No new changes detected since your last save.",
				color: "orange",
			});
			setEditing?.(false);
			return;
		}

		const summaryWordCount = values.professionalLifeSummary
			? values.professionalLifeSummary.trim().split(/\s+/).length
			: 0;
		if (summaryWordCount > 100) {
			notifications.show({
				title: "Validation Error",
				message: "Summary must be 100 words or fewer.",
				color: "red",
			});
			return;
		}

		const professionalLife = {
			...values,
			firstJob: {
				...values.firstJob,
				roles: values.firstJob.roles.filter(r => r.trim() !== ""),
			},
			subsequentJobs: values.subsequentJobs.map(job => ({
				...job,
				roles: (job.roles ?? []).filter(r => r.trim() !== ""),
			})),
			professionalLifeTags: values.professionalLifeTags.filter(
				tag => tag.trim() !== ""
			),
		};

		try {
			const response = await apiClient.post(
				"/api/lifeData/update",
				{
					professionalLife,
					userId,
				},
				{ withCredentials: true }
			);

			notifications.show({
				title: "Success",
				message: response.data.message,
				color: "green",
			});
			form.setInitialValues(professionalLife);
			fetchUser();
			onFormSuccess?.();
			setEditing?.(false);
			fetchProfile?.();
		} catch (err) {
			if (isAxiosError(err)) {
				notifications.show({
					title: "Error",
					message:
						err?.response?.data?.message || "Failed to save data",
					color: "red",
				});
			} else {
				notifications.show({
					title: "Error",
					message: "Failed to save data",
					color: "red",
				});
			}
		}
	};

	const addSubsequentJob = () => {
		form.insertListItem("subsequentJobs", { companyName: "", roles: [] });
	};

	const removeSubsequentJob = (index: number) => {
		if (form.values.subsequentJobs.length <= 1 && !isEditable) return;
		form.removeListItem("subsequentJobs", index);
	};

	const handleCancel = () => {
		form.reset();
		setEditing?.(false);
	};
	if (loading) return <FullScreenLoader />;

	return (
		<Paper shadow="sm" p="xl" radius="md" withBorder>
			<Flex justify={"flex-end"} mb="sm">
				{editing && (
					<Button variant="outline" onClick={handleCancel}>
						Cancel
					</Button>
				)}
			</Flex>
			<VideoPreviewAndUpload
				editing={editing}
				videoPreviewUrl={form.values.videoUrl}
				setHasUnsavedChanges={setHasUnsavedChanges}
				videoType="ProfessionalLife"
				onboardingStepCompleted={onboardingStepCompleted}
				userId={userId}
			/>

			<Title order={2} size="h2" mb="xl">
				Professional Life Details
			</Title>
			<form>
				<Stack>
					<Stack>
						<Textarea
							label="Professional Life Summary"
							description="Max 100 words"
							autosize
							minRows={3}
							disabled={!isEditable}
							{...form.getInputProps("professionalLifeSummary")}
							onBlur={handleTrimBlur("professionalLifeSummary")}
						/>
					</Stack>

					<Title order={4}>First Job</Title>
					<TextInput
						label="Company Name"
						disabled={!isEditable}
						{...form.getInputProps("firstJob.companyName")}
						placeholder="eg: Google, Facebook, etc"
						onBlur={handleTrimBlur("firstJob.companyName")}
					/>

					<TagsInput
						label="Job Titles"
						placeholder={
							isEditable
								? "Add a title (eg: Analyst, Director, etc..) and press Enter"
								: ""
						}
						disabled={!isEditable}
						value={form.values.firstJob.roles}
						onChange={handleTagsChange("firstJob.roles")}
						onDuplicate={() =>
							setFirstJobRolesError(
								"Duplicate role is not allowed"
							)
						}
						onKeyDown={() => setFirstJobRolesError(null)}
						error={firstJobRolesError}
						styles={{
							pill: {
								whiteSpace: "pre",
							},
						}}
					/>

					<Title order={3}>Subsequent Jobs</Title>
					{form.values.subsequentJobs.length > 0 ? (
						form.values.subsequentJobs.map((_, i) => (
							<Flex key={i} align="center" gap="md">
								<Stack w={"100%"}>
									<Group
										align="center"
										justify="space-between"
									>
										<TextInput
											label="Company Name"
											style={{ flex: 1 }}
											{...form.getInputProps(
												`subsequentJobs.${i}.companyName`
											)}
											disabled={!isEditable}
											placeholder="eg: Google, Facebook, etc"
											onBlur={handleTrimBlur(
												`subsequentJobs.${i}.companyName`
											)}
										/>
									</Group>

									<Stack key={i}>
										<TagsInput
											label="Job Titles"
											placeholder={
												isEditable
													? "Add a title (eg: Analyst, Director, etc..) and press Enter"
													: ""
											}
											disabled={!isEditable}
											value={
												form.values.subsequentJobs[i]
													.roles
											}
											onChange={handleTagsChange(
												`subsequentJobs.${i}.roles`
											)}
											onDuplicate={() =>
												setSubsequentJobsRolesError(
													prev => ({
														...prev,
														[i]: "Duplicate role is not allowed",
													})
												)
											}
											onKeyDown={() =>
												setSubsequentJobsRolesError(
													prev => ({
														...prev,
														[i]: null,
													})
												)
											}
											error={
												subsequentJobsRolesError &&
												subsequentJobsRolesError[i]
											}
											styles={{
												pill: {
													whiteSpace: "pre",
												},
											}}
										/>
									</Stack>
								</Stack>

								{isEditable && (
									<ActionIcon
										variant="subtle"
										color="red"
										onClick={() => {
											openCustomModal({
												confirmCallback: () =>
													removeSubsequentJob(i),
											});
										}}
										disabled={
											form.values.subsequentJobs
												.length === 1 && isEditable
										}
									>
										<IconTrash size={20} />
									</ActionIcon>
								)}
							</Flex>
						))
					) : (
						<Text size="sm" c="dimmed" className="pl-1">
							No subsequent job to display.
						</Text>
					)}

					{isEditable && (
						<Button
							variant="dashed"
							onClick={addSubsequentJob}
							w={"100%"}
							size="sm"
							radius="md"
							leftSection={<IconPlus size={16} />}
						>
							Add Another Job
						</Button>
					)}

					<TagsInput
						label="Professional Life Tags"
						description="Max 10 tags"
						placeholder={
							isEditable ? "Add a tag and press Enter" : ""
						}
						maxTags={10}
						disabled={!isEditable}
						value={form.values.professionalLifeTags}
						onChange={handleTagsChange("professionalLifeTags")}
						onDuplicate={() =>
							setProfessionalLifeTagsError(
								"Duplicate tag is not allowed"
							)
						}
						onKeyDown={handleTagsKeyDown}
						error={professionalLifeTagsError}
						styles={{
							pill: {
								whiteSpace: "pre",
							},
						}}
					/>

					{isEditable && (
						<Group justify="flex-end">
							<Button
								w={100}
								onClick={e => {
									e.preventDefault();
									handleSubmit(form.values);
								}}
							>
								Save
							</Button>
						</Group>
					)}
				</Stack>
			</form>
		</Paper>
	);
};

export default ProfessionalLifeForm;
