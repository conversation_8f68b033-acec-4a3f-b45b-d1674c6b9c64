import { Paper } from "@mantine/core";
import { useAuth } from "../../../contexts/AuthContext";
import FullScreenLoader from "../../FullScreenLoader";
import { Card, Flex, Image, TextInput } from "@mantine/core";
import { IconPlus } from "@tabler/icons-react";
import { useNavigate, useParams } from "react-router-dom";
import { useState } from "react";
import { resolveImageUrl } from "../../../utils/imageUrl";
import EventPostFormModal from "../../modals/EventPostFormModal";
import { notifications } from "@mantine/notifications";
import EventPosts from "./EventPosts";

const FeedWrapper = () => {
	const { user } = useAuth();
	const eventId = useParams().eventId;
	const navigate = useNavigate();

	const [openPostForm, setOpenPostForm] = useState<boolean>(false);

	const navigateToProfile = (userId: string) => {
		if (userId === user?._id) {
			navigate("/profile");
			return;
		}
		navigate(`/search/${userId}`);
	};

	const closePostFormHandler = () => {
		setOpenPostForm(false);
	};

	if (!user) return <FullScreenLoader />;

	if (!eventId) {
		navigate("/");
		notifications.show({
			title: "Invalid Request",
			message: "Invalid request",
			color: "red",
		});
		return null;
	}

	return (
		<>
			<Paper p="md" pt={0}>
				<Card shadow="sm" withBorder radius="md">
					<Flex gap={12} justify="space-between" align="center">
						<Image
							src={resolveImageUrl(user.image)}
							alt={`${user.firstName} ${user.secondName}`}
							w={50}
							h={50}
							radius={"xl"}
							fallbackSrc={`https://api.dicebear.com/5.x/initials/svg?seed=${user.firstName} ${user.secondName}`}
							onClick={() => navigateToProfile?.(user._id)}
							style={{
								cursor: "pointer",
							}}
						/>

						<TextInput
							w={"100%"}
							placeholder="Start a post"
							pointer
							readOnly
							radius={"xl"}
							leftSection={<IconPlus size={16} />}
							onClick={() => setOpenPostForm(true)}
							styles={{
								input: {
									paddingBlock: "1.35rem",
								},
							}}
						/>
					</Flex>
				</Card>
				<>
					<EventPosts eventId={eventId} />
				</>
			</Paper>

			{openPostForm && (
				<>
					<EventPostFormModal
						opened={openPostForm}
						onClose={closePostFormHandler}
						eventId={eventId}
					/>
				</>
			)}
		</>
	);
};

export default FeedWrapper;
