import axios, { AxiosError } from "axios";

// Create an axios instance for our API
export const apiClient = axios.create({
	baseURL: import.meta.env.VITE_API_URL,
	withCredentials: true,
});

apiClient.interceptors.response.use(
	response => response,
	error => {
		if (error instanceof AxiosError && error.response?.status === 401) {
			const currentPath = window.location.pathname;
			const isLoginPage =
				currentPath === "/login" ||
				currentPath === "/forgot-password" ||
				currentPath.startsWith("/reset-password");

			if (!isLoginPage) {
				// Only show session expired message if user was previously logged in
				// Check if there was a previous session by looking for any indication of prior authentication
				const hadPreviousSession =
					document.cookie.includes("token=") ||
					localStorage.getItem("hadSession") === "true";

				window.location.assign("/login");

				if (hadPreviousSession) {
					localStorage.setItem("loggedOut", "true");
					localStorage.setItem("timeStamp", Date.now().toString());
				}
			}
		}
		return Promise.reject(error);
	}
);

// Default axios instance for other requests
export const createClient = (baseURL: string) => {
	return axios.create({
		baseURL,
		withCredentials: true,
	});
};

export default apiClient;
