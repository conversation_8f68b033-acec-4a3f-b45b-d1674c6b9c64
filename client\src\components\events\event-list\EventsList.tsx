import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import type { EventListType, PaginatedEventsResponse } from "../../../types";
import apiClient from "../../../config/axios";
import FullScreenLoader from "../../FullScreenLoader";
import { Center, Grid, Loader, Tabs } from "@mantine/core";
import { EventCard } from "./EventCard";
import { notifications } from "@mantine/notifications";
import { isAxiosError } from "axios";
import { AnimatePresence, motion } from "framer-motion";
import { useNavigate, useParams } from "react-router-dom";
import { useAuth } from "../../../contexts/AuthContext";
import { roleValues } from "../../../constants";
import Empty from "../../common/Empty";
import { useIntersection } from "@mantine/hooks";

const MotionGridCol = motion(Grid.Col);

const tabsValues = {
	liveEvents: "live-events",
	upcomingEvents: "upcoming-events",
	pastEvents: "past-events",
	draftEvents: "draft-events",
};

const LIMIT = 12;
const COOLDOWN_MS = 600;

type TabKey = keyof typeof tabsValues;

type TabValue = (typeof tabsValues)[TabKey];

const tabValueToKey: Record<TabValue, TabKey> = {
	[tabsValues.liveEvents]: "liveEvents",
	[tabsValues.upcomingEvents]: "upcomingEvents",
	[tabsValues.pastEvents]: "pastEvents",
	[tabsValues.draftEvents]: "draftEvents",
};

type PagesMap = { [K in TabKey]: number };

type HasMoreMap = { [K in TabKey]: boolean };

const EventsList = () => {
	const { user } = useAuth();
	const navigate = useNavigate();
	const { tab } = useParams();

	const [loading, setLoading] = useState<boolean>(false);
	const [loadingMore, setLoadingMore] = useState<boolean>(false);
	const [draftEvents, setDraftEvents] = useState<EventListType[]>([]);
	const [upcomingEvents, setUpcomingEvents] = useState<EventListType[]>([]);
	const [liveEvents, setLiveEvents] = useState<EventListType[]>([]);
	const [pastEvents, setPastEvents] = useState<EventListType[]>([]);

	const currentTab = useMemo(
		() => (tab ?? tabsValues.liveEvents) as TabValue,
		[tab]
	);
	const currentKey = useMemo(() => tabValueToKey[currentTab], [currentTab]);

	const [pages, setPages] = useState<PagesMap>({
		liveEvents: 0,
		upcomingEvents: 0,
		pastEvents: 0,
		draftEvents: 0,
	});

	const [hasMore, setHasMore] = useState<HasMoreMap>({
		liveEvents: true,
		upcomingEvents: true,
		pastEvents: true,
		draftEvents: true,
	});

	const loadingMoreRef = useRef(false);

	const wasIntersectingRef = useRef(false);
	const lastLoadAtRef = useRef(0);

	const { ref: sentinelRef, entry } = useIntersection({
		root: null,
		threshold: 0.5,
		rootMargin: "0px 0px 200px 0px",
	});

	const handleTabChange = (value: string | null) => {
		if (value === currentTab) return;
		navigate(`/events/${value}`);
	};

	const fetchFirstPage = useCallback(async () => {
		try {
			setLoading(true);
			const res = await apiClient.get<PaginatedEventsResponse>(
				"/api/events/get-events",
				{ params: { tab: currentTab, page: 1, limit: LIMIT } }
			);
			const payload = res.data;
			// Set list for current tab
			if (currentKey === "liveEvents") setLiveEvents(payload.data);
			if (currentKey === "upcomingEvents")
				setUpcomingEvents(payload.data);
			if (currentKey === "pastEvents") setPastEvents(payload.data);
			if (currentKey === "draftEvents") setDraftEvents(payload.data);
			setPages(prev => ({ ...prev, [currentKey]: 1 }));
			setHasMore(prev => ({ ...prev, [currentKey]: payload.hasMore }));
		} catch (err) {
			console.log(err);
		} finally {
			setLoading(false);
		}
	}, [currentTab, currentKey]);

	useEffect(() => {
		fetchFirstPage();
	}, [fetchFirstPage]);

	// When sentinel becomes visible, load next page for the active tab (edge + cooldown)
	useEffect(() => {
		const nowIntersecting = !!entry?.isIntersecting;
		const wasIntersecting = wasIntersectingRef.current;

		const canLoad =
			nowIntersecting &&
			!wasIntersecting &&
			!loading &&
			!loadingMore &&
			!loadingMoreRef.current &&
			hasMore[currentKey] &&
			pages[currentKey] >= 1 &&
			Date.now() - lastLoadAtRef.current >= COOLDOWN_MS;

		if (canLoad) {
			const load = async () => {
				try {
					loadingMoreRef.current = true;
					setLoadingMore(true);
					lastLoadAtRef.current = Date.now();
					const nextPage = pages[currentKey] + 1;
					const res = await apiClient.get<PaginatedEventsResponse>(
						"/api/events/get-events",
						{
							params: {
								tab: currentTab,
								page: nextPage,
								limit: LIMIT,
							},
						}
					);
					const payload = res.data;
					if (currentKey === "liveEvents")
						setLiveEvents(prev => [...prev, ...payload.data]);
					if (currentKey === "upcomingEvents")
						setUpcomingEvents(prev => [...prev, ...payload.data]);
					if (currentKey === "pastEvents")
						setPastEvents(prev => [...prev, ...payload.data]);
					if (currentKey === "draftEvents")
						setDraftEvents(prev => [...prev, ...payload.data]);
					setPages(prev => ({ ...prev, [currentKey]: nextPage }));
					setHasMore(prev => ({
						...prev,
						[currentKey]: payload.hasMore,
					}));
				} catch (e) {
					console.log(e);
				} finally {
					loadingMoreRef.current = false;
					setLoadingMore(false);
				}
			};
			load();
		}

		wasIntersectingRef.current = nowIntersecting;
	}, [
		entry?.isIntersecting,
		loading,
		loadingMore,
		hasMore,
		currentKey,
		currentTab,
		pages,
	]);

	// With server pagination, we render all fetched items; this helper now just returns items unchanged
	const getVisibleSlice = (items: EventListType[], _key?: TabKey) => items;

	// Backwards-compat for existing JSX checks; mirrors hasMore flags
	const visibleCounts: Record<TabKey, number> = {
		liveEvents: hasMore.liveEvents ? 0 : liveEvents.length,
		upcomingEvents: hasMore.upcomingEvents ? 0 : upcomingEvents.length,
		pastEvents: hasMore.pastEvents ? 0 : pastEvents.length,
		draftEvents: hasMore.draftEvents ? 0 : draftEvents.length,
	};

	const handleDelete = async (eventId: string) => {
		try {
			const response = await apiClient.delete(
				`/api/events/delete-event/${eventId}`
			);

			notifications.show({
				title: "Success",
				message: response.data.message,
				color: "green",
			});
			fetchFirstPage();
		} catch (error) {
			console.error(error);
			if (isAxiosError(error)) {
				notifications.show({
					title: "Failed",
					message:
						error.response?.data?.message ||
						"Failed to delete event",
					color: "red",
				});
			} else {
				notifications.show({
					title: "Failed",
					message: "Failed to delete event",
					color: "red",
				});
			}
		}
	};

	const handleEdit = (eventId: string, eventName: string) => {
		navigate(`/events/update-event/${eventName}/${eventId}`);
	};

	const handlePublish = async (eventId: string) => {
		try {
			setLoading(true);
			const response = await apiClient.put(
				`/api/events/publish-event/${eventId}`
			);

			notifications.show({
				title: "Success",
				message: response.data.message,
				color: "green",
			});
			fetchFirstPage();
		} catch (error) {
			console.error(error);
			if (isAxiosError(error)) {
				notifications.show({
					title: "Failed",
					message:
						error.response?.data?.message ||
						"Failed to publish event",
					color: "red",
				});
			} else {
				notifications.show({
					title: "Failed",
					message: "Failed to publish event",
					color: "red",
				});
			}
		} finally {
			setLoading(false);
		}
	};

	if (loading) {
		return <FullScreenLoader />;
	}
	return (
		<>
			<Tabs
				p={0}
				mt="md"
				defaultValue={currentTab}
				onChange={handleTabChange}
			>
				<Tabs.List>
					<Tabs.Tab value={tabsValues.liveEvents}>
						Live Events
					</Tabs.Tab>
					<Tabs.Tab value={tabsValues.upcomingEvents}>
						Upcoming Events
					</Tabs.Tab>
					<Tabs.Tab value={tabsValues.pastEvents}>
						Past Events
					</Tabs.Tab>
					{user?.role !== roleValues.CommunityMember && (
						<Tabs.Tab value={tabsValues.draftEvents}>
							Draft Events
						</Tabs.Tab>
					)}
				</Tabs.List>

				<Tabs.Panel value={tabsValues.liveEvents}>
					{liveEvents.length === 0 ? (
						<Empty messageText="There are no live events right now." />
					) : (
						<>
							<Grid gutter="xl" mt="xl">
								<AnimatePresence>
									{getVisibleSlice(
										liveEvents,
										"liveEvents"
									).map(event => (
										<MotionGridCol
											key={event._id}
											layout
											initial={{ opacity: 0, scale: 0.9 }}
											animate={{ opacity: 1, scale: 1 }}
											exit={{ opacity: 0, scale: 0.9 }}
											transition={{ duration: 0.3 }}
											span={{ base: 12, sm: 6, lg: 4 }}
										>
											<EventCard
												event={event}
												handleDelete={handleDelete}
												handleEdit={handleEdit}
											/>
										</MotionGridCol>
									))}
								</AnimatePresence>
							</Grid>
							{visibleCounts.liveEvents < liveEvents.length && (
								<>
									<div ref={sentinelRef} />
									<Center my="md">
										{loadingMore && <Loader size="sm" />}
									</Center>
								</>
							)}
						</>
					)}
				</Tabs.Panel>

				<Tabs.Panel value={tabsValues.upcomingEvents}>
					{upcomingEvents.length === 0 ? (
						<Empty messageText="No upcoming events are scheduled." />
					) : (
						<>
							<Grid gutter="xl" mt="xl">
								<AnimatePresence>
									{getVisibleSlice(
										upcomingEvents,
										"upcomingEvents"
									).map(event => (
										<MotionGridCol
											key={event._id}
											layout
											initial={{ opacity: 0, scale: 0.9 }}
											animate={{ opacity: 1, scale: 1 }}
											exit={{ opacity: 0, scale: 0.9 }}
											transition={{ duration: 0.3 }}
											span={{ base: 12, sm: 6, lg: 4 }}
										>
											<EventCard
												event={event}
												handleDelete={handleDelete}
												handleEdit={handleEdit}
											/>
										</MotionGridCol>
									))}
								</AnimatePresence>
							</Grid>
							{visibleCounts.upcomingEvents <
								upcomingEvents.length && (
								<>
									<div ref={sentinelRef} />
									<Center my="md">
										{loadingMore && <Loader size="sm" />}
									</Center>
								</>
							)}
						</>
					)}
				</Tabs.Panel>

				<Tabs.Panel value={tabsValues.pastEvents}>
					{pastEvents.length === 0 ? (
						<Empty messageText="No past events available." />
					) : (
						<>
							<Grid gutter="xl" mt="xl">
								<AnimatePresence>
									{getVisibleSlice(
										pastEvents,
										"pastEvents"
									).map(event => (
										<MotionGridCol
											key={event._id}
											layout
											initial={{ opacity: 0, scale: 0.9 }}
											animate={{ opacity: 1, scale: 1 }}
											exit={{ opacity: 0, scale: 0.9 }}
											transition={{ duration: 0.3 }}
											span={{ base: 12, sm: 6, lg: 4 }}
										>
											<EventCard
												event={event}
												handleDelete={handleDelete}
												handleEdit={handleEdit}
											/>
										</MotionGridCol>
									))}
								</AnimatePresence>
							</Grid>
							{visibleCounts.pastEvents < pastEvents.length && (
								<>
									<div ref={sentinelRef} />
									<Center my="md">
										{loadingMore && <Loader size="sm" />}
									</Center>
								</>
							)}
						</>
					)}
				</Tabs.Panel>

				{user?.role !== roleValues.CommunityMember && (
					<Tabs.Panel value={tabsValues.draftEvents}>
						{draftEvents.length === 0 ? (
							<Empty messageText="You have no draft events." />
						) : (
							<>
								<Grid gutter="xl" mt="xl">
									<AnimatePresence>
										{getVisibleSlice(
											draftEvents,
											"draftEvents"
										).map(event => (
											<MotionGridCol
												key={event._id}
												layout
												initial={{
													opacity: 0,
													scale: 0.9,
												}}
												animate={{
													opacity: 1,
													scale: 1,
												}}
												exit={{
													opacity: 0,
													scale: 0.9,
												}}
												transition={{ duration: 0.3 }}
												span={{
													base: 12,
													sm: 6,
													lg: 4,
												}}
											>
												<EventCard
													event={event}
													handleDelete={handleDelete}
													handleEdit={handleEdit}
													handlePublish={
														handlePublish
													}
													isDraft={true}
												/>
											</MotionGridCol>
										))}
									</AnimatePresence>
								</Grid>
								{visibleCounts.draftEvents <
									draftEvents.length && (
									<>
										<div ref={sentinelRef} />
										<Center my="md">
											{loadingMore && (
												<Loader size="sm" />
											)}
										</Center>
									</>
								)}
							</>
						)}
					</Tabs.Panel>
				)}
			</Tabs>
		</>
	);
};

export default EventsList;
