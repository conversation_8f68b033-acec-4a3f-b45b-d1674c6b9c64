export interface Session {
	_id: string;
	email: string;
	firstName: string;
	secondName: string;
	mobile: string;
	role: number;
	createdAt: string;
	onboardingStep: number;
	onboardingStepCompleted: boolean;
	profileStatus: profileStatusDataType;
	canApproveUser: string;
	isAdminPanelUser: boolean;
	isFirstLogin?: boolean;
	image: string;
}

export interface AuthContextType {
	user: Session | null;
	loading: boolean;
	isUpdatingUser: boolean;
	isSwitchingAdminPanel: boolean;
	login: (email: string, password: string) => Promise<Session | null>;
	logout: () => Promise<void>;
	setUser: (user: Session) => void;
	fetchUser: (isUpdate?: boolean) => Promise<Session | null>;
	switchAdminPanel: () => Promise<{ message: string }>;
}

export interface UserCreation {
	_id: string;
	firstName: string;
	middleName?: string;
	secondName: string;
	email: string;
	mobile: string;
	role: number;
	createdAt?: string;
	profileStatus: profileStatusDataType;
}

export interface UserProfile {
	firstName: string;
	middleName: string;
	secondName: string;
	currentOrganization: string;
	role: number;
	mobile?: string;
	city: string;
	address: string;
	twitter: string;
	instagram: string;
	linkedIn: string;
	otherSocialHandles: string[];
	introduction: string;
	quote: string;
	joy: string;
	contentLinks: string[];
	image: string | File;
	email: string;
	pincode: string;
}

export type videoDataType = "EarlyLife" | "ProfessionalLife" | "CurrentLife";

export type School = {
	name: string;
	location: string;
	_id?: string;
};

export type University = {
	name: string;
	course: string;
	location: string;
	_id?: string;
};

export type EarlyLifeDataType = {
	birthCity: string;
	hometownCity: string;
	schools: School[];
	universities: University[];
	earlyLifeTags: string[];
	videoUrl: string | null;
	earlyLifeSummary: string;
};

export type Job = {
	companyName: string;
	roles: string[];
	_id?: string;
};

export type ProfessionalLifeDataType = {
	firstJob: Job;
	subsequentJobs: Job[];
	professionalLifeTags: string[];
	videoUrl: string | null;
	professionalLifeSummary: string;
};

export type Organization = {
	name?: string;
	role?: string;
	_id?: string;
};

export type CurrentLifeDataType = {
	currentLifeSummary: string;
	currentCities: string[];
	frequentTravelCities: string[];
	currentOrganizations: Organization[];
	currentLifeTags: string[];
	videoUrl: string | null;
};

export type AllLifeDataType = {
	earlyLifeData: EarlyLifeDataType;
	professionalLifeData: ProfessionalLifeDataType;
	currentLifeData: CurrentLifeDataType;
};

export type previewApprovedBasicDetailsDataType = {
	firstName: string;
	middleName: string;
	secondName: string;
	currentOrganization: string;
	role: number;
	mobile: string;
	city: string;
	address: string;
	twitter: string;
	instagram: string;
	linkedIn: string;
	otherSocialHandles: string[];
	introduction: string;
	quote: string;
	joy: string;
	contentLinks: string[];
	image: string;
	email: string;
	pincode: string;
};

export type previewApprovedResponseDataType = {
	basicDetails: previewApprovedBasicDetailsDataType;
	earlyLifeData: EarlyLifeDataType;
	professionalLifeData: ProfessionalLifeDataType;
	currentLifeData: CurrentLifeDataType;
	profileStatus: profileStatusDataType;
	updatedEarlyLifeData?: EarlyLifeDataType;
	updatedProfessionalLifeData?: ProfessionalLifeDataType;
	updatedCurrentLifeData?: CurrentLifeDataType;
	updatedSections?: { key: string; label: string }[];
	feedbackCount?: number;
	onboardingStep: number;
};

export type profileStatusDataType =
	| "onboarding"
	| "pending"
	| "approved"
	| "re-approved"
	| "changes_requested";

export type activeFeedbackInPendingProfiles = {
	_id: string;
	feedbackMessage: FeedbackMessage;
	actionTaken: {
		basicDetails?: boolean;
		earlyLife?: boolean;
		professionalLife?: boolean;
		currentLife?: boolean;
	};
	isRead: boolean;
};

export type pendingProfilesDataType = {
	_id: string;
	firstName: string;
	secondName: string;
	email: string;
	role: number;
	onboardingStep: number;
	mobile: string;
	profileStatus: profileStatusDataType;
	resendToOnboarding?: boolean;
	activeFeedbacks?: activeFeedbackInPendingProfiles[];
	unreadFeedbacks?: number;
	actionCompleted?: boolean;
};

export type UserSearchResult = {
	_id: string;
	firstName: string;
	middleName: string;
	secondName: string;
	image: string;
	email: string;
	role: number;
};

export type UserRefererCrutorSearchResult = {
	_id: string;
	firstName: string;
	secondName: string;
	image: string;
	email: string;
	role: number;
	profileStatus: profileStatusDataType;
};

export type TotalCountType = {
	added: number;
	deleted: number;
	edited: number;
	total: number;
};

export type DiffChange = {
	kind: "N" | "D" | "E" | "A";
	path?: (string | number)[];
	lhs?: any;
	rhs?: any;
	index?: number;
	item?: DiffChange;
};

export type tabsDataType =
	| "basic-details"
	| "early-life"
	| "professional-life"
	| "current-life";

export interface Sender {
	_id: string;
	firstName: string;
	secondName: string;
	email: string;
}

export interface FeedbackMessage {
	basicDetails?: string;
	earlyLifeData?: string;
	professionalLifeData?: string;
	currentLifeData?: string;
}

export interface FeedbackDataType {
	_id: string;
	sender: Sender;
	feedbackMessage: FeedbackMessage;
	createdAt: string;
	isReadByCurrentUser: boolean;
	isReadByRecipient: boolean;
}

export interface NotificationCounts {
	totalCount: number;
	unreadCount: number;
}

export interface ActionInfoType {
	basicDetails?: boolean;
	earlyLife?: boolean;
	professionalLife?: boolean;
	currentLife?: boolean;
	actionCompleted?: boolean;
}
export interface NotificationContextType {
	feedbacks: FeedbackDataType[];
	counts: NotificationCounts;
	loading: boolean;
	viewMoreLoading: boolean;
	notificationsOpened: boolean;
	toggleNotifications: () => void;
	fetchFeedbacks: (
		isViewMore?: boolean,
		current_count?: number
	) => Promise<void>;
	loadMoreNotifications: () => void;
	decrementUnreadCount: () => void;
	getActionInfo: () => void;
	actionInfo: ActionInfoType;
}

export type EventUser = {
	_id: string;
	firstName: string;
	secondName: string;
	email: string;
	image: string;
};

export type EventListType = {
	_id: string;
	name: string;
	description: string;
	startDate: string;
	endDate: string;
	thumbnailImage?: {
		url: string;
	};
	currentUserAttendingStatus?: EventAttendanceStatusType;
};

export type EventListDataType = {
	draftEvents: EventListType[];
	upcomingEvents: EventListType[];
	pastEvents: EventListType[];
	liveEvents: EventListType[];
};

export type PaginatedEventsResponse = {
	data: EventListType[];
	total: number;
	page: number;
	hasMore: boolean;
};

export type EventFormValues = {
	_id?: string;
	name: string;
	description: string;
	startDate: Date | null;
	endDate: Date | null;
	detail: string;
	thumbnailImage?: string;
	creationMedia?: EventDetailsType["creationMedia"];
};

export type EventDetailsType = {
	_id: string;
	name: string;
	description: string;
	startDate: Date;
	endDate: Date;
	detail: string;
	thumbnailImage: {
		url: string;
	};
	creationMedia: {
		_id: string;
		url: string;
		isThumbnail: boolean;
	}[];
	createdBy: EventUser;
	counts: {
		totalPosts: number;
		approvedPosts: number;
		nonAttendingCount: number;
		attendeesCount: number;
		commentsCount: number;
	};
	status: string;
	createdAt: string;
	updatedAt: string;
	currentUserAttendingStatus?: EventAttendanceStatusType;
	eventTypes: {
		isUpcoming: boolean;
		isLive: boolean;
		isDraft: boolean;
		isPast: boolean;
	};
};

export type EventAttendanceStatusType = "attending" | "not_attending";

export type PostApprovedStatus = "pending" | "approved";

export type PostDataType = {
	_id: string;
	eventId: string;
	userId: EventUser;
	content: string;
	userTags: {
		id: string;
		firstName: string;
		secondName: string;
	};
	media: { url: string }[];
	createdAt: string;
	isApproved: PostApprovedStatus;
};

export type EventsPostResponseDataType = {
	data: PostDataType[];
	total: number;
	page: number;
	hasMore: boolean;
};
