import React, { useEffect, useMemo, useRef, useState } from "react";
import {
	Button,
	Paper,
	TextInput,
	Group,
	Stack,
	Textarea,
	Spoiler,
	Flex,
} from "@mantine/core";
import { Edit, Save } from "lucide-react";
import { Icon<PERSON>heck, IconX } from "@tabler/icons-react";
import { useForm } from "@mantine/form";
import type { UserProfile } from "../types";
import apiClient from "../config/axios";
import { notifications } from "@mantine/notifications";
import { isAxiosError } from "axios";
import FullScreenLoader from "../components/FullScreenLoader";
import {
	IMAGE_UPLOAD_MAX_SIZE_IN_MB,
	roleLabels,
	rolesLabelMap,
} from "../constants";
import { fallbackImage } from "../utils";
import { pincodeList } from "../data/pincodes";
import ImageCropperModal from "../components/modals/ImageCropperModal";
import BasicDetailsPreview from "../components/profile/BasicDetailsPreview";
import BasicDetails from "../components/profile-forms/BasicDetails";

type ProfileProps = {
	initialValues?: Partial<UserProfile>;
	saveUrl?: string;
	isEditable?: boolean;
	onStepDone?: () => void;
	setHasUnsavedChanges?: (hasChanges: boolean) => void;
	hideCancelButton?: boolean;
	noEditInProfile?: boolean;
	fetchProfile?: () => Promise<void>;
};

const Profile = (props: ProfileProps) => {
	const fileRef = useRef<HTMLInputElement>(null);
	const [isEditing, setIsEditing] = useState(props.isEditable ?? false);
	const [loading, setLoading] = useState(false);
	const inputRefs = useRef<
		Record<string, HTMLInputElement | HTMLTextAreaElement | null>
	>({});
	const imageRef = useRef<HTMLImageElement | null>(null);
	const [formImageObjectUrl, setFormImageObjectUrl] = useState<string | null>(
		null
	);
	const [isCropperOpen, setIsCropperOpen] = useState(false);
	const [fileToCrop, setFileToCrop] = useState<File | null>(null);

	const form = useForm<UserProfile>({
		initialValues: {
			firstName: "",
			secondName: "",
			image: "",
			mobile: "",
			address: "",
			city: "",
			introduction: "",
			quote: "",
			joy: "",
			contentLinks: [],
			currentOrganization: "",
			twitter: "",
			instagram: "",
			linkedIn: "",
			otherSocialHandles: [],
			middleName: "",
			email: "",
			role: 3,
			pincode: "",
			...props.initialValues,
		},
		validateInputOnChange: true, // runs validation while typing
		validateInputOnBlur: true, // runs validation when leaving field
		validate: {
			firstName: (value: string) =>
				value.trim() ? null : "First Name is required",

			secondName: (value: string) =>
				value.trim() ? null : "Last Name is required",
			currentOrganization: (value: string) =>
				value.trim() ? null : "Current Organization is required",
			city: (value: string) => (value.trim() ? null : "City is required"),
			// address: (value: string) =>
			// 	value.trim() ? null : "Address is required", // made address non-mandatory
			pincode: (value: string, values: UserProfile) => {
				if (!value.trim()) {
					return "Pincode is required";
				}
				if (values.mobile && values.mobile.startsWith("+91")) {
					if (!pincodeList.includes(values.pincode.trim())) {
						return "Invalid Pincode";
					}
				}
				return null;
			},
			introduction: (value: string) => {
				const trimmed = value.trim();
				if (!trimmed) return "Introduction is required";
				const wordCount = trimmed.split(/\s+/).length;
				return wordCount <= 100
					? null
					: "Introduction must not exceed 100 words";
			},
			quote: (value: string) => {
				const trimmed = value.trim();
				if (!trimmed) return "Quote is required";
				const lineCount = trimmed.split("\n").length;
				const wordCount = trimmed.split(/\s+/).length;
				if (wordCount > 100) {
					return "Quote must not exceed 100 words";
				}
				return lineCount <= 2 ? null : "Quote must not exceed 2 lines";
			},

			joy: (value: string) => {
				const trimmed = value.trim();
				if (!trimmed) return "Joy is required";
				const lineCount = trimmed.split("\n").length;
				const wordCount = trimmed.split(/\s+/).length;
				if (wordCount > 100) {
					return " What fills you with joy must not exceed 100 words";
				}
				return lineCount <= 2
					? null
					: "What fills you with joy must not exceed 2 lines";
			},
			image: (value: string | File | null) => {
				// The value here is either a string (existing URL) or a File (newly cropped)
				if (!value) return "Image is required";
				if (typeof value === "string") {
					return value.trim() === "" ? "Image is required" : null;
				}
				if (value instanceof File) {
					return null;
				}
				return "Image is required";
			},
			twitter: (value: string) => {
				if (!value.trim()) return null;
				try {
					// new URL(value.trim()); // reason for comment: valid URL like www.google.com is not allowed in new URL
					return null;
				} catch {
					return "Enter a valid Twitter URL";
				}
			},

			instagram: (value: string) => {
				if (!value.trim()) return null;
				try {
					// new URL(value.trim()); // reason for comment: valid URL like www.google.com is not allowed in new URL
					return null;
				} catch {
					return "Enter a valid Instagram URL";
				}
			},

			linkedIn: (value: string) => {
				if (!value.trim()) return null;
				try {
					// new URL(value.trim()); // reason for comment: valid URL like www.google.com is not allowed in new URL
					return null;
				} catch {
					return "Enter a valid LinkedIn URL";
				}
			},

			otherSocialHandles: (value: string[]) => {
				if (!value || value.length === 0) return null;
				for (const link of value) {
					if (!link.trim()) continue;
					try {
						//new URL(link.trim()); // reason for comment: valid URL like www.google.com is not allowed in new URL
					} catch {
						return "Enter valid URLs for social handles";
					}
				}
				return null;
			},

			contentLinks: (value: string[]) => {
				if (!value || value.length === 0) return null;
				for (const link of value) {
					if (!link.trim()) continue;
					try {
						//new URL(link.trim()); // reason for comment: valid URL like www.google.com is not allowed in new URL
					} catch {
						return "Enter valid URLs for content links";
					}
				}
				return null;
			},
		},
		transformValues: values => ({
			...values,
			firstName: values.firstName.trim(),
			secondName: values.secondName.trim(),
			middleName: values.middleName.trim(),
			image:
				typeof values.image === "string"
					? values.image.trim()
					: values.image,
			mobile: values.mobile?.trim(),
			address: values.address.trim(),
			city: values.city.trim(),
			introduction: values.introduction.trim(),
			quote: values.quote.trim(),
			joy: values.joy.trim(),
			currentOrganization: values.currentOrganization.trim(),
			twitter: values.twitter.trim(),
			instagram: values.instagram.trim(),
			linkedIn: values.linkedIn.trim(),
			email: values.email.trim(),
			contentLinks: values.contentLinks.filter(
				link => link.trim() !== ""
			),
			otherSocialHandles: values.otherSocialHandles.filter(
				handle => handle.trim() !== ""
			),
		}),
	});

	useEffect(() => {
		if (form.values.image instanceof File) {
			const newUrl = URL.createObjectURL(form.values.image);
			setFormImageObjectUrl(newUrl);
			return () => {
				URL.revokeObjectURL(newUrl);
			};
		} else {
			setFormImageObjectUrl(null);
		}
	}, [form.values.image]);

	const handleError = (errors: typeof form.errors) => {
		const firstErrorField = Object.keys(errors)[0];
		if (firstErrorField && inputRefs.current[firstErrorField]) {
			inputRefs.current[firstErrorField]?.scrollIntoView({
				behavior: "smooth",
				block: "center",
			});

			inputRefs.current[firstErrorField]?.focus({ preventScroll: true });
		}
	};

	const fetchProfile = async () => {
		setLoading(true);
		try {
			const response = await apiClient<UserProfile>(
				"/api/users/user-profile"
			);
			const data = response.data;

			const getArray = (
				value: string | string[] | undefined | null
			): string[] => {
				if (Array.isArray(value)) {
					return value;
				}
				if (typeof value === "string" && value.trim().startsWith("[")) {
					try {
						const parsed = JSON.parse(value);
						return Array.isArray(parsed) ? parsed : [];
					} catch (e) {
						console.log(e);
						return [];
					}
				}
				return [];
			};

			const profileData: UserProfile = {
				firstName: data.firstName ?? "",
				secondName: data.secondName ?? "",
				image: data.image ?? "",
				mobile: data.mobile ?? "",
				address: data.address ?? "",
				city: data.city ?? "",
				introduction: data.introduction ?? "",
				quote: data.quote ?? "",
				joy: data.joy ?? "",
				contentLinks: getArray(data.contentLinks),
				currentOrganization: data.currentOrganization ?? "",
				twitter: data.twitter ?? "",
				instagram: data.instagram ?? "",
				linkedIn: data.linkedIn ?? "",
				otherSocialHandles: getArray(data.otherSocialHandles),
				middleName: data.middleName ?? "",
				email: data.email ?? "",
				role: data.role ?? 3,
				pincode: data.pincode ?? "",
			};

			form.setValues(profileData);
			form.setInitialValues(profileData);
		} catch (err) {
			console.error("err: ", err);
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		if (!props.initialValues) {
			fetchProfile();
		}
	}, []);

	const { setHasUnsavedChanges } = props;
	const isDirty = form.isDirty();

	useEffect(() => {
		if (setHasUnsavedChanges) {
			setHasUnsavedChanges(isDirty);
		}
	}, [isDirty, setHasUnsavedChanges]);

	const renderField = (
		label: string,
		field: keyof UserProfile,
		icon?: React.ReactNode,
		disabled?: boolean,
		options?: { textarea?: boolean; placeholder?: string },
		isRequired?: boolean
	) => {
		const isLinkField = ["twitter", "instagram", "linkedIn"].includes(
			field
		);
		const value = form.values[field] as string;
		const SPOILER_FIELDS = ["introduction", "quote", "joy"];
		const useSpoiler = SPOILER_FIELDS.includes(field);

		return isEditing ? (
			options?.textarea ? (
				<Stack gap={0}>
					<Textarea
						disabled={disabled}
						required={isRequired}
						label={label}
						autosize={false}
						leftSection={icon}
						size="sm"
						radius="md"
						className="text-sm"
						placeholder={options?.placeholder}
						{...form.getInputProps(field)}
						error={
							form.errors[field] ? form.errors[field] : undefined
						}
						styles={{
							input: {
								height: "140px",
							},
							section: {
								display: "flex",
								alignItems: "flex-start",
								justifyContent: "center",
								marginTop: "0.45rem",
							},
						}}
						ref={el => {
							inputRefs.current[field] = el;
						}}
					/>
				</Stack>
			) : (
				<TextInput
					disabled={disabled}
					required={isRequired}
					label={label}
					leftSection={icon}
					size="sm"
					radius="md"
					className="text-sm"
					placeholder={options?.placeholder}
					{...(field === "role"
						? {
								value: rolesLabelMap[
									roleLabels[
										value as unknown as keyof typeof roleLabels
									]
								],
							}
						: form.getInputProps(field))}
					error={form.errors[field] ? form.errors[field] : undefined}
					ref={el => {
						inputRefs.current[field] = el;
					}}
				/>
			)
		) : (
			<div className="flex items-start gap-3 w-full max-w-full">
				<div className="mt-0.5 text-gray-500 shrink-0">{icon}</div>

				<div className="w-full overflow-hidden">
					<p className="text-sm text-gray-500 font-medium mb-1">
						{label}
					</p>
					{isLinkField ? (
						value && (value as string).trim() !== "" ? (
							<a
								href={
									value.includes("://")
										? value
										: `https://${value}`
								}
								target="_blank"
								rel="noopener noreferrer"
								className="text-blue-600 text-md hover:text-blue-800 truncate block w-full"
							>
								{value}
							</a>
						) : (
							<span className="text-gray-500 truncate block w-full">
								-
							</span>
						)
					) : useSpoiler ? (
						<Spoiler
							maxHeight={80}
							showLabel="Show more"
							hideLabel="Show less"
							styles={{
								control: {
									fontSize: "14px",
								},
							}}
						>
							<p className="text-md text-gray-800 whitespace-pre-wrap break-words">
								{value && (value as string).trim() !== ""
									? value
									: "-"}
							</p>
						</Spoiler>
					) : (
						<p
							className={`text-md text-gray-800 w-full ${
								options?.textarea
									? "whitespace-pre-wrap break-words"
									: "truncate overflow-hidden whitespace-nowrap"
							}`}
						>
							{typeof value === "string" &&
							(value as string).trim() !== ""
								? value
								: typeof value === "number"
									? rolesLabelMap[
											roleLabels[
												value as keyof typeof roleLabels
											]
										]
									: "-"}
						</p>
					)}
				</div>
			</div>
		);
	};

	// IMAGE CHANGE HANDLER
	const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0];
		if (file) {
			if (!file.type.startsWith("image/")) {
				notifications.show({
					title: "Invalid Image",
					message: "Please upload a valid image file.",
					color: "red",
				});
				e.target.value = "";
				return;
			}

			const maxSizeInBytes = IMAGE_UPLOAD_MAX_SIZE_IN_MB * 1024 * 1024;
			if (file.size > maxSizeInBytes) {
				notifications.show({
					title: "Image Size Exceeded",
					message: `Image size should not exceed ${IMAGE_UPLOAD_MAX_SIZE_IN_MB}MB.`,
					color: "red",
				});
				e.target.value = "";
				return;
			}
			setFileToCrop(file);
			setIsCropperOpen(true);
		}
	};

	const handleCropDone = (croppedFile: File) => {
		form.setFieldValue("image", croppedFile);
		// setPreview(URL.createObjectURL(croppedFile));
		setIsCropperOpen(false);
		setFileToCrop(null);
		if (fileRef.current) {
			fileRef.current.value = "";
		}
	};

	const handleCropCancel = () => {
		setIsCropperOpen(false);
		setFileToCrop(null);
		if (fileRef.current) {
			fileRef.current.value = "";
		}
	};

	const handleSave = async () => {
		const validation = form.validate();

		if (validation.errors.image && imageRef.current) {
			imageRef.current.scrollIntoView({
				behavior: "smooth",
				block: "center",
			});
			return;
		}

		if (validation.hasErrors) {
			handleError(validation.errors);
			return;
		}

		const formData = new FormData();
		Object.entries(form.getTransformedValues()).forEach(([key, value]) => {
			if (value === undefined || value === null) return;

			if (
				Array.isArray(value) ||
				(typeof value === "object" && key !== "image")
			) {
				formData.append(key, JSON.stringify(value));
			} else if (key === "image" && value instanceof File) {
				formData.append("image", value);
			} else {
				formData.append(key, value as string);
			}
		});

		if (Object.keys(form.errors).length > 0) {
			return;
		}

		try {
			const response = await apiClient.post(
				props.saveUrl || "/api/users/update-profile",
				formData,
				{
					headers: {
						"Content-Type": "multipart/form-data",
					},
				}
			);
			notifications.show({
				title: "Updated",
				message: response.data.message,
				color: "green",
				icon: <IconCheck />,
			});
			if (!props.isEditable) {
				setIsEditing(false);
			}

			if (props.fetchProfile) {
				await props.fetchProfile();
			} else if (!props.initialValues) {
				await fetchProfile();
			}
			props.onStepDone?.();
		} catch (err) {
			console.error("err: ", err);
			if (isAxiosError(err)) {
				notifications.show({
					title: "Failed",
					message:
						err?.response?.data?.message ??
						err?.message ??
						"Failed to update profile",
					color: "red",
					icon: <IconX />,
				});
			} else {
				notifications.show({
					title: "Failed",
					message: "Failed to update profile",
					color: "red",
					icon: <IconX />,
				});
			}
		}
	};

	const imageSrc = useMemo(() => {
		return (
			formImageObjectUrl ||
			(form.values.image
				? `${import.meta.env.VITE_API_URL}${form.values.image}`
				: fallbackImage({
						firstName: form.values.firstName,
						lastName: form.values.secondName,
					}))
		);
	}, [
		formImageObjectUrl,
		form.values.image,
		form.values.firstName,
		form.values.secondName,
	]);

	if (loading) {
		return <FullScreenLoader />;
	}

	return (
		<div className="min-h-screen">
			<ImageCropperModal
				isOpen={isCropperOpen}
				fileToCrop={fileToCrop}
				onCropDone={handleCropDone}
				onCancel={handleCropCancel}
			/>
			<Paper p="lg" pt={"md"}>
				{((!props.hideCancelButton && isEditing) ||
					(!props.noEditInProfile && !isEditing)) && (
					<Flex justify="flex-end" pb={"md"}>
						{!props.noEditInProfile && !isEditing && (
							<Button
								onClick={() => setIsEditing(true)}
								leftSection={<Edit size={16} />}
							>
								Edit
							</Button>
						)}

						{!props.hideCancelButton && isEditing && (
							<Button
								variant="outline"
								onClick={() => {
									setIsEditing(false);
									form.reset();
									if (fileRef.current) {
										fileRef.current.value = "";
									}
									setIsCropperOpen(false);
									setFileToCrop(null);
								}}
							>
								Cancel
							</Button>
						)}
					</Flex>
				)}

				{isEditing ? (
					<>
						<BasicDetails
							form={form}
							imageSrc={imageSrc}
							imageRef={imageRef}
							fileRef={fileRef}
							handleImageChange={handleImageChange}
							renderField={renderField}
						/>
					</>
				) : (
					<BasicDetailsPreview
						basicDetails={form.values}
						showRole={false}
					/>
				)}

				<Group justify="flex-end" align="center" mt="lg">
					{!props.noEditInProfile && isEditing && (
						<Button
							onClick={handleSave}
							leftSection={<Save size={16} />}
						>
							Save
						</Button>
					)}
				</Group>
			</Paper>
		</div>
	);
};

export default Profile;
