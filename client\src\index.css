@import "tailwindcss";

.mantine-Button-root[data-variant="danger"] {
  background-color: #ef4444; /* Red background */
  color: white;
  border: 1px solid #ef4444;
  
  &:hover {
    background-color: #F05656; /* Darker red on hover */
    border-color: #F05656; 
  }
  
  &:active {
    background-color: #dc2626; /* Even darker when pressed */
  }
  
  &:disabled {
    background-color: #fca5a5; /* Light red when disabled */
    color: #9ca3af;
    cursor: not-allowed;
  }
}

.mantine-Button-root[data-variant="dashed"] {
  border: 1px dashed var(--mantine-color-blue-6); /* Blue dotted border */
  color: #3b82f6; /* Blue text */
  background-color: transparent;
  padding: 0.5rem 1rem; /* Adjust padding as needed */
  border-radius: 0.5rem; /* Adjust border radius as needed */
}