import type {
	EventAttendanceStatusType,
	PostApprovedStatus,
	profileStatusDataType,
} from "../types";

export const roleLabels = {
	1: "SuperAdmin",
	2: "Admin",
	3: "CommunityMember",
} as const;

export const rolesLabelMap = {
	SuperAdmin: "Super Admin",
	Admin: "Admin",
	CommunityMember: "Community Member",
};

export const roleValues = {
	SuperAdmin: 1,
	Admin: 2,
	CommunityMember: 3,
} as const;

export const allowedRoles = ["SuperAdmin", "Admin", "CommunityMember"];

export const MAX_RECORDING_TIME = 15 * 60;

export const videoTypeMap = {
	"1": "Early Life",
	"2": "Professional Life",
	"3": "Current Life",
};

export const videoTypeLabel = {
	EarlyLife: "Early Life",
	ProfessionalLife: "Professional Life",
	CurrentLife: "Current Life",
};

export const VIDEO_UPLOAD_MAX_SIZE_IN_MB = 2000;
export const IMAGE_UPLOAD_MAX_SIZE_IN_MB = 2;

export const profileStatusColorMap: Record<profileStatusDataType, string> = {
	onboarding: "blue",
	pending: "yellow",
	approved: "green",
	"re-approved": "teal",
	changes_requested: "orange",
};

export const ONBOARDING_STEP = {
	BASIC_DETAILS: 1,
	EARLY_LIFE_VIDEO: 2,
	EARLY_LIFE_FORM: 3,
	PROFESSIONAL_LIFE_VIDEO: 4,
	PROFESSIONAL_LIFE_FORM: 5,
	CURRENT_LIFE_VIDEO: 6,
	CURRENT_LIFE_FORM: 7,
	FINAL_SUBMIT: 8,
	WAIT_FOR_APPROVAL: 9,
	COMPLETED: 10,
};

export const ONBOARDING_STEP_MESSAGE = {
	[ONBOARDING_STEP.BASIC_DETAILS]: "User is at the Basic Details step.",
	[ONBOARDING_STEP.EARLY_LIFE_VIDEO]:
		"User is at the Early Life (Video) step.",
	[ONBOARDING_STEP.EARLY_LIFE_FORM]:
		"User is at the Early Life (Details) step.",
	[ONBOARDING_STEP.PROFESSIONAL_LIFE_VIDEO]:
		"User is at the Professional Life (Video) step.",
	[ONBOARDING_STEP.PROFESSIONAL_LIFE_FORM]:
		"User is at the Professional Life (Details) step.",
	[ONBOARDING_STEP.CURRENT_LIFE_VIDEO]:
		"User is at the Current Life (Video) step.",
	[ONBOARDING_STEP.CURRENT_LIFE_FORM]:
		"User is at the Current Life (Details) step.",
	[ONBOARDING_STEP.FINAL_SUBMIT]:
		"User is at the Final Review & Submit step.",
	[ONBOARDING_STEP.WAIT_FOR_APPROVAL]:
		"User has submitted their profile and waiting for admin approval.",
	[ONBOARDING_STEP.COMPLETED]: "✅ User has completed onboarding.",
};

export const DEBOUNCE_TIME_IN_MS = 500;

export const tabsMap = {
	"Basic Details": "basic-details",
	"Early Life Details": "early-life",
	"Professional Life Details": "professional-life",
	"Current Life Details": "current-life",
};

export const tabsMapReverse = Object.fromEntries(
	Object.entries(tabsMap).map(([key, value]) => [value, key])
);

export const sortOptions = [
	{ label: "Email", value: "email" },
	{ label: "First Name", value: "firstName" },
	{ label: "Last Name", value: "secondName" },
	{ label: "Last Updated", value: "updatedAt" },
];

export const ProfileStatusMapping = {
	Onboarding: "onboarding",
	Pending: "pending",
	ChangesRequested: "changes_requested",
	Approved: "approved",
	ReApproved: "re-approved",
};

export const profileStatus = [
	{ label: "All", value: "all" },
	{ label: "Onboarding", value: "onboarding" },
	{ label: "Pending Review", value: "pending" },
	{ label: "Awaiting Re-Approval", value: "re-approved" },
	{ label: "Approved", value: "approved" },
	{ label: "Changes Requested", value: "changes_requested" },
];

export const APP_NAME = "Gang 360";
export const PUBLIC_NAME = "Supermorpheus";
export const ADMIN_EMAIL = "<EMAIL>";
export const ADMIN_PHONE = "+XX-XXXXXXXXXX";
export const PUBLIC_CONTACT_NAME = "Yatin";

export const videoTranscriptionStatusMapping = {
	pending: "pending",
	processing: "processing",
	completed: "completed",
	failed: "failed",
};

export const EVENT_STATUS = {
	Published: "published",
	Unpublished: "unpublished",
};

export const EVENT_ATTENDANCE_STATUS = {
	Attending: "attending" as EventAttendanceStatusType,
	NotAttending: "not_attending" as EventAttendanceStatusType,
};

export const EVENT_MEDIA_SOURCE = {
	Creation: "creation",
	Community: "community",
};

export const POST_APPROVED_STATUS = {
	Pending: "pending" as PostApprovedStatus,
	Approved: "approved" as PostApprovedStatus,
};
