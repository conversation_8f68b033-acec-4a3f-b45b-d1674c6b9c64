import React, { useState } from "react";
import ProfessionalLifePreview from "../profile/ProfessionalLifePreview";
import type { ProfessionalLifeDataType } from "../../types";
import ProfessionalLifeForm from "../profile-forms/ProfessionalLifeForm";

interface ProfessionalLifeProps {
	professionalLifeData: ProfessionalLifeDataType;
	fetchProfile: () => void;
	userId?: string;
	updatedProfessionalLifeData?: ProfessionalLifeDataType;
	isEditable?: boolean;
	onboardingStepCompleted: boolean;
}

const ProfessionalLife: React.FC<ProfessionalLifeProps> = ({
	professionalLifeData,
	fetchProfile,
	userId,
	updatedProfessionalLifeData,
	isEditable,
	onboardingStepCompleted,
}) => {
	const [editing, setEditing] = useState(false);

	return (
		<>
			{editing ? (
				<div style={{ padding: "2rem 1rem 1rem 1rem" }}>
					<ProfessionalLifeForm
						fetchProfile={fetchProfile}
						editing={editing}
						setEditing={setEditing}
						lifeData={
							updatedProfessionalLifeData ?? professionalLifeData
						}
						userId={userId}
						onboardingStepCompleted={onboardingStepCompleted}
					/>
				</div>
			) : (
				<ProfessionalLifePreview
					showEdit={isEditable ?? true}
					setEditing={setEditing}
					lifeData={professionalLifeData}
					updatedLifeData={updatedProfessionalLifeData}
					userId={userId}
				/>
			)}
		</>
	);
};

export default ProfessionalLife;
