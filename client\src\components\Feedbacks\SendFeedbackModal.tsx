import {
	Button,
	Checkbox,
	Flex,
	Modal,
	Paper,
	Stack,
	Text,
	Textarea,
	Title,
} from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { isAxiosError } from "axios";
import { ArrowLeft, Send } from "lucide-react";
import { useEffect, useState } from "react";
import apiClient from "../../config/axios";
import { useForm } from "@mantine/form";
import type { profileStatusDataType, tabsDataType } from "../../types";
import { useNavigate } from "react-router-dom";

type SendFeedbackModalProps = {
	opened: boolean;
	onClose: () => void;
	userId: string | undefined;
	profileStatus: profileStatusDataType;
	activeTab: tabsDataType | null;
	updatedSections: { key: string; label: string }[];
};

interface CategoryOption {
	id: tabsDataType;
	label: string;
	description: string;
}

const categories: CategoryOption[] = [
	{
		id: "basic-details",
		label: "Basic Details",
		description: "Personal information, contact details etc.",
	},
	{
		id: "early-life",
		label: "Early Life",
		description: "Birth & hometown, education etc.",
	},
	{
		id: "professional-life",
		label: "Professional Life",
		description: "First Job, subsequent jobs etc.",
	},
	{
		id: "current-life",
		label: "Current Life",
		description: "Organizations, frequent travel cities etc.",
	},
];

const SendFeedbackModal = (props: SendFeedbackModalProps) => {
	const navigate = useNavigate();
	const [step, setStep] = useState<
		"categories" | "feedback" | "confirmation"
	>("categories");
	const [selectedCategories, setSelectedCategories] = useState<
		tabsDataType[]
	>([]);
	const [isSubmitting, setIsSubmitting] = useState(false);

	const form = useForm({
		initialValues: {
			"basic-details": "",
			"early-life": "",
			"current-life": "",
			"professional-life": "",
		},
		validate: values => {
			const errors: Partial<Record<tabsDataType, string>> = {};
			selectedCategories.forEach(category => {
				if (!values[category]?.trim()) {
					errors[category] = "Feedback cannot be empty";
				}
			});
			return errors;
		},
	});

	const handleCategoryToggle = (categoryId: tabsDataType) => {
		setSelectedCategories(prev => {
			if (prev.includes(categoryId)) {
				return prev.filter(id => id !== categoryId);
			} else {
				const newCategories = [...prev, categoryId];
				const order: tabsDataType[] = [
					"basic-details",
					"early-life",
					"professional-life",
					"current-life",
				];
				return newCategories.sort(
					(a, b) => order.indexOf(a) - order.indexOf(b)
				);
			}
		});
	};

	const handleNextStep = () => {
		if (selectedCategories.length > 0) {
			form.clearErrors();
			setStep("feedback");
		}
	};

	const handleBackStep = () => setStep("categories");

	const handleCloseModal = () => {
		props.onClose();
		setStep("categories");
		setSelectedCategories([]);
		form.reset();
	};

	const showConfirmation = () => setStep("confirmation");

	const handleSendFeedback = async (values: typeof form.values) => {
		setIsSubmitting(true);
		try {
			await apiClient.post("/api/feedbacks/send-feedback", {
				userId: props.userId,
				feedback: selectedCategories.reduce(
					(acc, cat) => {
						acc[cat] = values[cat];
						return acc;
					},
					{} as Record<tabsDataType, string>
				),
			});
			notifications.show({
				title: "Success",
				message: "Feedback sent successfully.",
				color: "green",
			});
			handleCloseModal();
			if (props.profileStatus === "pending") {
				navigate("/pending-profiles");
			}
		} catch (error) {
			if (isAxiosError(error)) {
				notifications.show({
					title: "Error",
					message:
						error.response?.data?.message ||
						"Failed to send feedback",
					color: "red",
				});
			} else {
				notifications.show({
					title: "Error",
					message: "Failed to send feedback",
					color: "red",
				});
			}
		} finally {
			setIsSubmitting(false);
		}
	};

	useEffect(() => {
		if (props.profileStatus === "re-approved") {
			if (
				props.updatedSections?.length > 0 &&
				props.updatedSections?.some(sec => sec.key === props.activeTab)
			) {
				setSelectedCategories([props.activeTab as tabsDataType]);
			}
		} else {
			setSelectedCategories([props.activeTab as tabsDataType]);
		}
	}, [props.activeTab, props.profileStatus, props.updatedSections]);

	return (
		<>
			<Modal
				opened={props.opened}
				onClose={handleCloseModal}
				centered
				size="lg"
				title={
					<Stack gap={0}>
						<Title order={3}>
							{step === "confirmation"
								? "Confirm Submission"
								: step === "categories"
									? "Send Feedback"
									: "Provide Feedback"}
						</Title>
						<Text size="sm" c={"dimmed"}>
							{step === "confirmation"
								? "Review the implications below before sending your feedback."
								: step === "categories"
									? "Select the categories you want to provide feedback for"
									: "Share your feedback for the selected categories"}
						</Text>
					</Stack>
				}
				padding={24}
				trapFocus={false}
			>
				{step === "categories" ? (
					<Stack gap="md">
						<Stack gap="sm">
							{(props.profileStatus === "re-approved" &&
							props.updatedSections?.length > 0
								? props.updatedSections
										.map(sec => {
											const category = categories.find(
												c => c.id === sec.key
											);
											if (!category) {
												console.warn(
													`Unknown updated section key : ${sec.key}`
												);
												return null;
											}
											return {
												id: sec.key as tabsDataType,
												label: sec.label,
												description:
													category?.description || "",
											};
										})
										.filter(Boolean)
								: categories
							).map(category =>
								category ? (
									<Paper
										key={category.id}
										style={{
											display: "flex",
											alignItems: "flex-start",
											gap: 8,
											cursor: "pointer",
										}}
										withBorder
										radius="md"
										p="sm"
										onClick={() =>
											handleCategoryToggle(category.id)
										}
									>
										<Checkbox
											id={category.id}
											checked={selectedCategories.includes(
												category.id
											)}
											color="var(--mantine-color-blue-4)"
											styles={{
												inner: {
													marginTop: 4,
													marginLeft: 0,
												},
												input: {
													borderColor:
														"var(--mantine-color-blue-4)",
													cursor: "pointer",
												},
											}}
										/>
										<Stack gap={0}>
											<Text id={category.id} size="sm">
												{category.label}
											</Text>
											{category.description && (
												<Text
													size="xs"
													c="dimmed"
													mt={4}
												>
													{category.description}
												</Text>
											)}
										</Stack>
									</Paper>
								) : null
							)}
						</Stack>
						<Flex gap={"md"} justify="flex-end" mt="md">
							<Button
								variant="outline"
								onClick={handleCloseModal}
							>
								Cancel
							</Button>
							<Button
								onClick={handleNextStep}
								disabled={selectedCategories.length === 0} // disables until at least one selected
							>
								Next
							</Button>
						</Flex>
					</Stack>
				) : step === "feedback" ? (
					<form onSubmit={form.onSubmit(showConfirmation)}>
						<Stack gap={"md"}>
							{selectedCategories.map(categoryId => {
								const category = categories.find(
									c => c.id === categoryId
								);
								if (!category) {
									console.warn(
										`Unknown category id: ${categoryId}`
									);
									return null;
								}
								return (
									<Stack key={category.id} gap="xs">
										<Textarea
											label={category.label}
											required
											placeholder={`Share your feedback about ${category.label.toLowerCase()}...`}
											styles={{
												input: { height: "120px" },
											}}
											style={{
												whiteSpace: "pre-line",
											}}
											{...form.getInputProps(categoryId)}
										/>
									</Stack>
								);
							})}
						</Stack>
						<Flex
							gap={"md"}
							style={{
								position: "sticky",
								bottom: 0,
								background: "white",
								padding: "1rem 0",
								marginTop: "1rem",
							}}
							justify="flex-end"
						>
							<Button variant="outline" onClick={handleBackStep}>
								<ArrowLeft className="w-4 h-4 mr-2" />
								Back
							</Button>
							<Button
								type="submit"
								disabled={selectedCategories.some(
									cat => !form.values[cat]?.trim()
								)}
							>
								<Send className="w-4 h-4 mr-2" />
								Send Feedback
							</Button>
						</Flex>
					</form>
				) : (
					<Stack>
						<Text>
							{props.profileStatus === "pending"
								? "Sending feedback will move the user’s profile back to the onboarding stage. The profile cannot be approved again until the user resubmits it for approval."
								: "Please confirm that you want to send this feedback."}
						</Text>
						<Flex justify="flex-end" gap="md" mt="md">
							<Button
								variant="default"
								onClick={() => setStep("feedback")}
							>
								Cancel
							</Button>
							<Button
								color="red"
								onClick={() => handleSendFeedback(form.values)}
								loading={isSubmitting}
							>
								Confirm & Send
							</Button>
						</Flex>
					</Stack>
				)}
			</Modal>
		</>
	);
};

export default SendFeedbackModal;
