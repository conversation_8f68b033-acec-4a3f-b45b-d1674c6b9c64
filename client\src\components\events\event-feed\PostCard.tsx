import {
	Avatar,
	Card,
	Group,
	Text,
	ActionIcon,
	Box,
	Stack,
	Menu,
} from "@mantine/core";
import {
	IconDotsVertical,
	IconPencil,
	IconTrash,
	IconUserShare,
} from "@tabler/icons-react";
import "react-quill-new/dist/quill.snow.css";
import { resolveImageUrl } from "../../../utils/imageUrl";
import type { PostDataType } from "../../../types";
import openCustomModal from "../../modals/CustomModal";
import { useAuth } from "../../../contexts/AuthContext";
import { useMemo } from "react";
import ImageGrid from "./ImageGrid";

interface PostCardProps {
	post: PostDataType;
	deletePost: (postId: string) => void;
}

const PostCard = ({ post, deletePost }: PostCardProps) => {
	const { user } = useAuth();
	const { userId, content, media, createdAt } = post;

	const userIdName = `${userId.firstName} ${userId.secondName || ""}`.trim();
	const postDate = new Date(createdAt).toLocaleString();

	const { isOwnPost, isApprovedPost } = useMemo(() => {
		if (!user) return { isOwnPost: false, isApprovedPost: false };

		return {
			isOwnPost: userId._id === user._id,
			isApprovedPost: post.isApproved === "approved",
		};
	}, [user, userId._id, post.isApproved]);

	return (
		<Card shadow="sm" padding="lg" radius="md" withBorder mt={"md"}>
			<Card.Section withBorder inheritPadding py="xs">
				<Group justify="space-between">
					<Group>
						<Avatar
							src={resolveImageUrl(userId.image)}
							alt={userId.firstName}
							radius="xl"
						/>
						<Stack gap={0}>
							<Text fw={500}>{userIdName}</Text>
							<Text size="xs" c="dimmed">
								{postDate}
							</Text>
						</Stack>
					</Group>
					<Menu
						position="bottom-end"
						shadow="sm"
						styles={{
							dropdown: {
								minWidth: "140px",
								borderRadius: "var(--mantine-radius-md)",
							},
							item: {
								padding: "0.25rem",
								color: "var(--mantine-color-gray-8)",
							},
						}}
					>
						<Menu.Target>
							<ActionIcon variant="subtle" c="black">
								<IconDotsVertical size={16} />
							</ActionIcon>
						</Menu.Target>
						<Menu.Dropdown>
							{isOwnPost && (
								<>
									<Menu.Item
										onClick={() => {
											alert("Edit in progress");
										}}
									>
										<Group gap={6}>
											<IconPencil size={14} />
											Edit
										</Group>
									</Menu.Item>
									<Menu.Item
										onClick={() =>
											openCustomModal({
												title: "Are you sure you want to delete this post?",
												confirmCallback: () => {
													deletePost(post._id);
												},
											})
										}
									>
										<Group gap={6}>
											<IconTrash size={14} />
											Delete
										</Group>
									</Menu.Item>
								</>
							)}

							{!isApprovedPost && (
								<Menu.Item
									onClick={() => {
										openCustomModal({
											title: "Published Event",
											description:
												"Are you sure you want to publish this event?",
											confirmCallback: () => {},
										});
									}}
									variant="default"
									leftSection={<IconUserShare size={14} />}
								>
									Publish
								</Menu.Item>
							)}
						</Menu.Dropdown>
					</Menu>
				</Group>
			</Card.Section>

			<Box
				mt="md"
				className="ql-snow"
				style={{
					fontSize: "14px",
				}}
			>
				<div
					className="ql-editor"
					dangerouslySetInnerHTML={{ __html: content }}
					style={{
						padding: 0,
						lineHeight: 1.55,
						fontSize: "14px",
					}}
				/>
			</Box>

			{media && media.length > 0 && (
				<Card.Section pt={"md"}>
					<ImageGrid media={media} />
				</Card.Section>
			)}
		</Card>
	);
};

export default PostCard;
