import React, { useState } from "react";
import CurrentLifePreview from "../profile/CurrentLifePreview";
import CurrentLifeForm from "../profile-forms/CurrentLifeForm";
import type { CurrentLifeDataType } from "../../types";

interface CurrentLifeProps {
	currentLifeData: CurrentLifeDataType;
	fetchProfile: () => void;
	userId?: string;
	updatedCurrentLifeData?: CurrentLifeDataType;
	isEditable?: boolean;
	onboardingStepCompleted?: boolean;
}

const CurrentLife: React.FC<CurrentLifeProps> = ({
	currentLifeData,
	fetchProfile,
	userId,
	updatedCurrentLifeData,
	isEditable,
	onboardingStepCompleted,
}) => {
	const [editing, setEditing] = useState(false);
	return (
		<>
			{editing ? (
				<div style={{ padding: "2rem 1rem 1rem 1rem" }}>
					<CurrentLifeForm
						fetchProfile={fetchProfile}
						editing={editing}
						setEditing={setEditing}
						lifeData={updatedCurrentLifeData ?? currentLifeData}
						userId={userId}
						onboardingStepCompleted={onboardingStepCompleted}
					/>
				</div>
			) : (
				<CurrentLifePreview
					showEdit={isEditable ?? true}
					setEditing={setEditing}
					lifeData={currentLifeData}
					updatedLifeData={updatedCurrentLifeData}
					userId={userId}
				/>
			)}
		</>
	);
};

export default CurrentLife;
