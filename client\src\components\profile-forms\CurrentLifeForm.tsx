import { useAuth } from "../../contexts/AuthContext";
import {
	TextInput,
	Paper,
	Title,
	Button,
	Stack,
	Group,
	Textarea,
	ActionIcon,
	Text,
	Flex,
} from "@mantine/core";
import React, { useEffect, useCallback, useState } from "react";
import { TagsInput } from "@mantine/core";
import { IconPlus, IconTrash, IconX } from "@tabler/icons-react";
import apiClient from "../../config/axios";
import { notifications } from "@mantine/notifications";
import { isAxiosError } from "axios";
import type { CurrentLifeDataType } from "../../types";
import VideoPreviewAndUpload from "../VideoPreviewAndUpload";
import { useForm } from "@mantine/form";
import openCustomModal from "../modals/CustomModal";
import FullScreenLoader from "../FullScreenLoader";

interface CurrentLifeProps {
	onFormSuccess?: () => void;
	isEditable?: boolean;
	lifeData: CurrentLifeDataType | null;
	setHasUnsavedChanges?: (hasChanges: boolean) => void;
	setResetForm?: (resetFunc: () => void) => void;
	setEditing?: (value: boolean) => void;
	editing?: boolean;
	fetchProfile?: () => void;
	userId?: string;
	onboardingStepCompleted?: boolean;
}

const CurrentLifeForm: React.FC<CurrentLifeProps> = ({
	onFormSuccess,
	lifeData,
	isEditable = true,
	setHasUnsavedChanges,
	setResetForm,
	setEditing,
	editing,
	fetchProfile,
	userId,
	onboardingStepCompleted,
}) => {
	const { user } = useAuth();
	const { fetchUser } = useAuth();
	const [loading, setLoading] = useState<boolean>(false);

	const [currentCitiesError, setCurrentCitiesError] = useState<string | null>(
		null
	);
	const [frequentTravelCitiesError, setFrequentTravelCitiesError] = useState<
		string | null
	>(null);
	const [currentLifeTagsError, setCurrentLifeTagsError] = useState<
		string | null
	>(null);

	const form = useForm<CurrentLifeDataType>({
		initialValues: {
			currentLifeSummary: "",
			currentCities: [],
			frequentTravelCities: [],
			currentOrganizations: [],
			currentLifeTags: [],
			videoUrl: "",
		},
		validateInputOnChange: true,
		validateInputOnBlur: true,
		validate: {
			currentLifeSummary: (value: string) => {
				const trimmed = (value || "").trim();
				if (!trimmed) return "Early Life Summary is required";
				const wordCount = trimmed.split(/\s+/).filter(Boolean).length;
				return wordCount <= 100
					? null
					: "Current Life Summary must not exceed 100 words";
			},
		},
		transformValues: values => ({
			...values,
			currentLifeSummary: values.currentLifeSummary?.trim() || "",
			currentOrganizations: Array.isArray(values.currentOrganizations)
				? values.currentOrganizations.map(org => ({
						name: org?.name?.trim() || "",
						role: org?.role?.trim() || "",
						_id: org._id,
					}))
				: [],
		}),
	});

	const fetchData = useCallback(async () => {
		try {
			setLoading(true);
			const response = await apiClient.get<CurrentLifeDataType>(
				"/api/lifeData/currentLife"
			);
			const data = response.data;

			const fetchedData = {
				...data,
				currentOrganizations:
					(data?.currentOrganizations ?? []).length > 0
						? data.currentOrganizations
						: isEditable
							? [{ name: "", role: "" }]
							: [],
			};
			form.setValues(fetchedData);
			form.setInitialValues(fetchedData);
			setLoading(false);
		} catch (err) {
			console.error(err);
		}
	}, [isEditable]);

	useEffect(() => {
		if (lifeData) {
			const updatedLifeData = {
				...lifeData,
				currentOrganizations:
					(lifeData.currentOrganizations?.length ?? 0) > 0
						? lifeData.currentOrganizations
						: isEditable
							? [{ name: "", role: "" }]
							: [],
			};
			form.setValues(updatedLifeData);
			form.setInitialValues(updatedLifeData);
		} else {
			fetchData();
		}
	}, [lifeData, isEditable, fetchData]);

	const resetForm = useCallback(() => {
		form.reset();
	}, [form]);

	useEffect(() => {
		setResetForm?.(resetForm);
	}, [resetForm]);

	useEffect(() => {
		setHasUnsavedChanges?.(form.isDirty());
	}, [form.values]);

	const addOrganization = () => {
		form.insertListItem("currentOrganizations", { name: "", role: "" });
	};

	const removeOrganization = (index: number) => {
		if (form.values.currentOrganizations.length <= 1) return;
		form.removeListItem("currentOrganizations", index);
	};

	const handleTrimBlur = useCallback(
		(fieldName: string) =>
			(e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
				form.setFieldValue(fieldName, e.target.value.trim());
			},
		[form]
	);

	const handleTagsChange = useCallback(
		(fieldName: string) => (tags: string[]) => {
			const trimmed = tags.map(tag => tag.trim()).filter(Boolean);

			form.setFieldValue(fieldName, trimmed);
		},
		[form]
	);

	const handleTagsKeyDown = (
		event: React.KeyboardEvent<HTMLInputElement>
	) => {
		const targetValue = (event.target as HTMLInputElement).value.trim();

		if (
			event.key === "Enter" &&
			form.values.currentLifeTags.length >= 10 &&
			targetValue
		) {
			setCurrentLifeTagsError("You can only add up to 10 tags");
			event.preventDefault();
		} else {
			setCurrentLifeTagsError(null);
		}
	};

	const handleSubmit = async (values: CurrentLifeDataType) => {
		if (currentCitiesError) {
			notifications.show({
				icon: <IconX />,
				title: "Validation Error",
				message: currentCitiesError,
				color: "red",
			});
			return;
		}
		if (frequentTravelCitiesError) {
			notifications.show({
				icon: <IconX />,
				title: "Validation Error",
				message: frequentTravelCitiesError,
				color: "red",
			});
			return;
		}
		if (currentLifeTagsError) {
			notifications.show({
				icon: <IconX />,
				title: "Validation Error",
				message: currentLifeTagsError,
				color: "red",
			});
			return;
		}
		if (!form.isDirty() && user && user.onboardingStepCompleted) {
			notifications.show({
				title: "You’re all set",
				message: "No new changes detected since your last save.",
				color: "orange",
			});
			setEditing?.(false);
			return;
		}
		const summaryWordCount = values.currentLifeSummary
			? values.currentLifeSummary.trim().split(/\s+/).length
			: 0;
		if (summaryWordCount > 100) {
			notifications.show({
				title: "Validation Error",
				message: "Summary must be 100 words or fewer.",
				color: "red",
			});
			return;
		}

		const currentLife = {
			...values,
			currentOrganizations: values.currentOrganizations.map(org => ({
				...org,
				name: org?.name?.trim() ?? "",
				role: org?.role?.trim() ?? "",
			})),
		};

		try {
			const response = await apiClient.post(
				"/api/lifeData/update",
				{
					currentLife,
					userId,
				},
				{ withCredentials: true }
			);

			notifications.show({
				title: "Success",
				message: response.data.message,
				color: "green",
			});

			form.setInitialValues(currentLife);
			fetchUser();
			onFormSuccess?.();
			setEditing?.(false);
			fetchProfile?.();
		} catch (err) {
			if (isAxiosError(err)) {
				notifications.show({
					title: "Error",
					message:
						err?.response?.data?.message || "Failed to save data",
					color: "red",
				});
			} else {
				notifications.show({
					title: "Error",
					message: "Failed to save data",
					color: "red",
				});
			}
		}
	};

	const handleCancel = () => {
		form.reset();
		setEditing?.(false);
	};

	if (loading) return <FullScreenLoader />;

	return (
		<Paper shadow="sm" p="xl" radius="md" withBorder>
			<Flex justify={"flex-end"} mb="sm">
				{editing && (
					<Button variant="outline" onClick={handleCancel}>
						Cancel
					</Button>
				)}
			</Flex>
			<VideoPreviewAndUpload
				editing={editing}
				videoPreviewUrl={form.values.videoUrl}
				setHasUnsavedChanges={setHasUnsavedChanges}
				videoType="CurrentLife"
				onboardingStepCompleted={onboardingStepCompleted}
				userId={userId}
			/>

			<Title order={2} size="h2" mb="xl">
				Current Life Details
			</Title>
			<form>
				<Stack>
					<Stack gap={0}>
						<Textarea
							label="Current Life Summary"
							description="Max 100 words"
							autosize
							minRows={3}
							disabled={!isEditable}
							{...form.getInputProps("currentLifeSummary")}
							onBlur={handleTrimBlur("currentLifeSummary")}
						/>
					</Stack>

					<Stack gap={4}>
						<TagsInput
							label="Current Cities"
							placeholder="Add a city and press Enter"
							disabled={!isEditable}
							value={form.values.currentCities}
							onChange={handleTagsChange("currentCities")}
							onDuplicate={() =>
								setCurrentCitiesError(
									"Duplicate cities are not allowed"
								)
							}
							onKeyDown={() => {
								setCurrentCitiesError(null);
							}}
							error={currentCitiesError}
							styles={{
								pill: {
									whiteSpace: "pre",
								},
							}}
						/>
					</Stack>

					<Title order={4}>Current Organizations</Title>
					{form.values.currentOrganizations.length > 0 ? (
						form.values.currentOrganizations.map((_, index) => (
							<Group key={index} align="flex-end" gap="xs">
								<TextInput
									label="Name"
									style={{ flex: 1 }}
									disabled={!isEditable}
									{...form.getInputProps(
										`currentOrganizations.${index}.name`
									)}
									onBlur={handleTrimBlur(
										`currentOrganizations.${index}.name`
									)}
								/>
								<TextInput
									label="Role"
									style={{ flex: 1 }}
									disabled={!isEditable}
									{...form.getInputProps(
										`currentOrganizations.${index}.role`
									)}
									onBlur={handleTrimBlur(
										`currentOrganizations.${index}.role`
									)}
								/>
								{isEditable && (
									<ActionIcon
										color="red"
										variant="subtle"
										onClick={() => {
											openCustomModal({
												confirmCallback: () =>
													removeOrganization(index),
											});
										}}
										disabled={
											form.values.currentOrganizations
												.length === 1
										}
									>
										<IconTrash size={16} />
									</ActionIcon>
								)}
							</Group>
						))
					) : (
						<Text size="sm" c="dimmed" className="pl-1">
							No organization to display.
						</Text>
					)}
					{isEditable && (
						<Button
							variant="dashed"
							onClick={addOrganization}
							w={"100%"}
							size="sm"
							radius="md"
							leftSection={<IconPlus size={16} />}
						>
							Add Organization
						</Button>
					)}

					<Stack gap={4}>
						<TagsInput
							label="Frequent Travel Cities"
							placeholder="Add a city and press Enter"
							disabled={!isEditable}
							value={form.values.frequentTravelCities}
							onChange={handleTagsChange("frequentTravelCities")}
							onDuplicate={() =>
								setFrequentTravelCitiesError(
									"Duplicate cities are not allowed"
								)
							}
							onKeyDown={() => setFrequentTravelCitiesError(null)}
							error={frequentTravelCitiesError}
							styles={{
								pill: {
									whiteSpace: "pre",
								},
							}}
						/>
					</Stack>
					<Stack gap={4}>
						<TagsInput
							label="Current Life Tags"
							placeholder={
								isEditable ? "Add a tag and press Enter" : ""
							}
							description="Max 10 tags"
							maxTags={10}
							disabled={!isEditable}
							value={form.values.currentLifeTags}
							onChange={handleTagsChange("currentLifeTags")}
							onDuplicate={() =>
								setCurrentLifeTagsError(
									"Duplicate tags are not allowed"
								)
							}
							onKeyDown={handleTagsKeyDown}
							error={currentLifeTagsError}
							styles={{
								pill: {
									whiteSpace: "pre",
								},
							}}
						/>
					</Stack>

					{isEditable && (
						<Group justify="flex-end">
							<Button
								w={100}
								onClick={e => {
									e.preventDefault();
									handleSubmit(form.values);
								}}
							>
								Save
							</Button>
						</Group>
					)}
				</Stack>
			</form>
		</Paper>
	);
};

export default CurrentLifeForm;
