import {
	Stack,
	Text,
	Divider,
	Flex,
	Button,
	ThemeIcon,
	Group,
	Paper,
} from "@mantine/core";
import React, { useEffect, useState } from "react";
import apiClient from "../../config/axios";
import type { DiffChange, ProfessionalLifeDataType } from "../../types";
import VideoPreviewAndUpload from "../VideoPreviewAndUpload";
import FullScreenLoader from "../FullScreenLoader";
import { getChangedValue, getDiff } from "../../utils/deep-diff";
import JobDisplay from "./common/JobDisplay";
import LifeTags from "./common/LifeTags";
import { IconInfoCircle, IconTags } from "@tabler/icons-react";
import DiffCount from "./common/DiffCount";
import RenderDiff from "./common/RenderDiff";
import { Edit } from "lucide-react";

interface ProfessionalLifePreviewProps {
	showEdit?: boolean;
	setEditing?: (value: boolean) => void;
	lifeData?: ProfessionalLifeDataType;
	updatedLifeData?: ProfessionalLifeDataType;
	userId?: string;
}

const ProfessionalLifePreview: React.FC<ProfessionalLifePreviewProps> = ({
	showEdit,
	setEditing,
	lifeData,
	updatedLifeData,
	userId,
}) => {
	const [professionalLife, setProfessionalLife] =
		useState<ProfessionalLifeDataType | null>(null);
	const [diffChanges, setDiffChanges] = useState<DiffChange[] | null>(null);
	const [totalChangesCount, setSetTotalChangesCount] = useState<number>(0);

	const fetchData = async () => {
		try {
			const response = await apiClient.get<ProfessionalLifeDataType>(
				"/api/lifeData/professionalLife"
			);
			setProfessionalLife(response.data);
		} catch (err) {
			console.error(`Error fetching professional life data: ${err}`);
		}
	};

	useEffect(() => {
		if (!userId && !lifeData) {
			fetchData();
		} else if (lifeData) {
			setProfessionalLife(lifeData);
		}
	}, [lifeData, userId]);

	useEffect(() => {
		if (lifeData && updatedLifeData) {
			const deepDiff = getDiff({
				originalData: lifeData,
				updatedData: updatedLifeData,
				ignoredKeys: ["videoUrl"],
			});

			if (deepDiff && deepDiff.changes.length > 0) {
				setDiffChanges(deepDiff.changes);
				setSetTotalChangesCount(deepDiff.total);
			} else {
				setDiffChanges(null);
			}
		}
	}, [lifeData, updatedLifeData]);

	if (!professionalLife) return <FullScreenLoader />;

	return (
		<Paper p="md">
			{showEdit && (
				<Flex justify="space-between">
					{totalChangesCount > 0 && (
						<DiffCount totalChanges={totalChangesCount} />
					)}
					<Button
						onClick={() => setEditing?.(true)}
						mb={16}
						ml={"auto"}
						leftSection={<Edit size={16} />}
					>
						Edit
					</Button>
				</Flex>
			)}
			<VideoPreviewAndUpload
				editing={true}
				videoPreviewUrl={professionalLife.videoUrl}
				videoType="ProfessionalLife"
				userId={userId}
			/>

			<Stack gap="lg">
				<Flex gap="xs" align="flex-start">
					<Group>
						<ThemeIcon variant="light" color="blue">
							<IconInfoCircle size={16} />
						</ThemeIcon>
					</Group>
					<Stack gap={0} justify="flex-start">
						<Text c="dimmed" size="xs">
							Summary:
						</Text>
						{(() => {
							const diff = getChangedValue(
								["professionalLifeSummary"],
								diffChanges
							);
							return diff ? (
								<RenderDiff elementType="text" {...diff} />
							) : (
								<RenderDiff
									elementType="text"
									oldValue={
										professionalLife.professionalLifeSummary
									}
									newValue={null}
									type={"unchanged"}
									size="sm"
								/>
							);
						})()}
					</Stack>
				</Flex>

				<Divider label="First Job" labelPosition="center" />

				<JobDisplay
					job={professionalLife.firstJob}
					diffChanges={diffChanges}
					diffPathPrefix={["firstJob"]}
					isFirstJob={true}
				/>

				<Divider label="Subsequent Jobs" labelPosition="center" />

				{/* Subsequent Jobs */}
				{(() => {
					const subsequentJobsToRender =
						professionalLife.subsequentJobs
							? [...professionalLife.subsequentJobs]
							: [];
					if (diffChanges) {
						const newJobDiffs = diffChanges.filter(
							change =>
								change.kind === "A" &&
								change.item?.kind === "N" &&
								JSON.stringify(change.path) ===
									JSON.stringify(["subsequentJobs"])
						);

						newJobDiffs.forEach(diff => {
							if (
								diff.index &&
								diff.kind === "A" &&
								diff.item?.kind === "N"
							) {
								subsequentJobsToRender.splice(
									diff.index,
									0,
									diff.item.rhs
								);
							}
						});
					}

					if (
						subsequentJobsToRender &&
						subsequentJobsToRender.length > 0
					) {
						return subsequentJobsToRender.map((job, i) => (
							<React.Fragment key={i}>
								<JobDisplay
									job={job}
									diffChanges={diffChanges}
									diffPathPrefix={["subsequentJobs", i]}
									isFirstJob={false}
								/>
								{i < subsequentJobsToRender.length - 1 && (
									<Divider className="my-2 border-gray-200" />
								)}
							</React.Fragment>
						));
					} else {
						return (
							<Text size="sm" c="dimmed">
								No Subsequent Jobs to display.
							</Text>
						);
					}
				})()}

				{/* Tags */}
				<LifeTags
					tags={professionalLife.professionalLifeTags}
					diffChanges={diffChanges}
					diffPathPrefix="professionalLifeTags"
					icon={
						<ThemeIcon variant="light" color="yellow" mt={2}>
							<IconTags size={16} />
						</ThemeIcon>
					}
					dividerLabelProps={{
						label: "Tags",
						labelPosition: "center",
					}}
				/>
			</Stack>
		</Paper>
	);
};

export default ProfessionalLifePreview;
