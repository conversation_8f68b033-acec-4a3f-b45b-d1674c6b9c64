import { Modal } from "@mantine/core";
import { IconVideo } from "@tabler/icons-react";
import type { videoDataType } from "../../types";
import Instruction from "./Instruction";
import { InstructionData } from "./InstructionData";

interface VideoInstructionsModalProps {
	opened: boolean;
	onClose: () => void;
	videoType: videoDataType;
}

const VideoInstructionsModal: React.FC<VideoInstructionsModalProps> = ({
	opened,
	onClose,
	videoType,
}) => {
	const instructions = InstructionData(videoType);

	return (
		<Modal
			opened={opened}
			onClose={onClose}
			withCloseButton={false}
			title={
				<div
					style={{
						display: "flex",
						alignItems: "center",
						gap: "8px",
						fontSize: "1.25rem",
						fontWeight: 600,
					}}
				>
					<IconVideo size="1.5rem" />
					{instructions.title}
				</div>
			}
			size="lg"
			centered
			trapFocus={false}
		>
			<Instruction videoType={videoType} onClose={onClose} />
		</Modal>
	);
};

export default VideoInstructionsModal;
