import fs from "fs";
import path from "path";
import ffmpeg from "fluent-ffmpeg";
import ffmpegStatic from "ffmpeg-static";
import ffprobeStatic from "ffprobe-static";
import { THUMBNAIL_DIR } from "../constants/paths.js";

// Derive string paths to binaries (ffmpeg-static returns a string; ffprobe-static exposes .path)
const ffmpegBinary =
	typeof ffmpegStatic === "string" ? ffmpegStatic : ffmpegStatic?.path;
const ffprobeBinary =
	typeof ffprobeStatic === "string" ? ffprobeStatic : ffprobeStatic?.path;

if (!ffmpegBinary || typeof ffmpegBinary !== "string") {
	throw new Error("ffmpeg binary path not found from ffmpeg-static.");
}
if (!ffprobeBinary || typeof ffprobeBinary !== "string") {
	throw new Error(
		"ffprobe binary path not found from ffprobe-static (use ffprobeStatic.path)."
	);
}

ffmpeg.setFfmpegPath(ffmpegBinary);
ffmpeg.setFfprobePath(ffprobeBinary);

/**
 * Get video duration in seconds
 * @param {string} videoPath - Path to the video file
 * @returns {Promise<number>} Duration in seconds
 */
const getVideoDuration = async videoPath => {
	return new Promise((resolve, reject) => {
		ffmpeg.ffprobe(videoPath, (err, metadata) => {
			if (err) return reject(new Error(`ffprobe failed: ${err.message}`));
			try {
				const duration = metadata?.format?.duration;
				if (!duration || typeof duration !== "number") {
					throw new Error("Could not determine video duration");
				}
				resolve(duration);
			} catch (e) {
				reject(
					new Error(
						`Failed to parse video duration: ${(e && e.message) || e}`
					)
				);
			}
		});
	});
};

/**
 * Extract multiple thumbnails from video at random timestamps
 * @param {string} videoPath - Path to the video file
 * @param {string} videoId - Video ID for naming thumbnails
 * @param {number} count - Number of thumbnails to extract (default: 3)
 * @returns {Promise<string[]>} Array of thumbnail file paths
 */
export const extractThumbnailsFromVideo = async (
	videoPath,
	videoId,
	count = 3
) => {
	if (!fs.existsSync(videoPath)) {
		throw new Error(`Video file not found: ${videoPath}`);
	}

	const inputPath = path.resolve(videoPath);

	// Get video duration
	const duration = await getVideoDuration(inputPath);

	// Create thumbnails directory
	fs.mkdirSync(THUMBNAIL_DIR, { recursive: true });

	// Calculate random timestamps for thumbnail extraction
	// Skip first and last 10% to avoid black frames or credits
	const startOffset = duration * 0.1;
	const endOffset = duration * 0.9;
	const extractableDuration = endOffset - startOffset;

	// Generate random timestamps
	const timestamps = [];
	const usedTimestamps = new Set();

	while (timestamps.length < count) {
		const randomTimestamp =
			startOffset + Math.random() * extractableDuration;
		const roundedTimestamp = Math.round(randomTimestamp * 10) / 10; // Round to 1 decimal place

		// Ensure we don't have duplicate timestamps
		if (!usedTimestamps.has(roundedTimestamp)) {
			timestamps.push(roundedTimestamp);
			usedTimestamps.add(roundedTimestamp);
		}
	}

	// Sort timestamps for consistent ordering
	timestamps.sort((a, b) => a - b);

	console.log(`Extracting ${count} thumbnails from video: ${inputPath}`);
	console.log(`Video duration: ${duration}s, timestamps:`, timestamps);

	const thumbnailPaths = [];

	// Extract thumbnails sequentially to avoid overwhelming the system
	for (let i = 0; i < timestamps.length; i++) {
		const timestamp = timestamps[i];
		const thumbnailPath = path.join(
			THUMBNAIL_DIR,
			`${videoId}_thumbnail_${i + 1}.jpg`
		);

		await new Promise((resolve, reject) => {
			ffmpeg(inputPath)
				.seekInput(timestamp)
				.frames(1)
				.format("image2")
				.on("end", () => {
					console.log(
						`Thumbnail ${i + 1} extracted: ${thumbnailPath}`
					);
					resolve();
				})
				.on("error", err => {
					reject(
						new Error(
							`Failed to extract thumbnail ${i + 1}: ${err?.message || err}`
						)
					);
				})
				.save(thumbnailPath);
		});

		if (!fs.existsSync(thumbnailPath)) {
			throw new Error(`Thumbnail ${i + 1} not created by FFmpeg.`);
		}

		thumbnailPaths.push(path.resolve(thumbnailPath));
	}

	console.log(`Successfully extracted ${thumbnailPaths.length} thumbnails`);
	return thumbnailPaths;
};

/**
 * Clean up thumbnail files
 * @param {string[]} thumbnailPaths - Array of thumbnail file paths to delete
 */
export const cleanupThumbnails = thumbnailPaths => {
	thumbnailPaths.forEach(thumbnailPath => {
		if (fs.existsSync(thumbnailPath)) {
			try {
				fs.unlinkSync(thumbnailPath);
				console.log(`Cleaned up thumbnail: ${thumbnailPath}`);
			} catch (error) {
				console.error(
					`Failed to cleanup thumbnail ${thumbnailPath}:`,
					error
				);
			}
		}
	});
};
