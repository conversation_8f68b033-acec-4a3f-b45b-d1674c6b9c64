import express from "express";
import { checkLogin } from "../middleware/auth.js";
import {
	uploadVideo,
	getVideoStatus,
	confirmUpload,
	getVideoThumbnails,
	selectThumbnail,
	getSelectedThumbail,
	getCustomThumbnailSignedUrl,
	confirmCustomThumbnailUpload,
} from "../controllers/video.controller.js";

const router = express.Router();

router.post("/upload", checkLogin, uploadVideo);
router.post("/upload/success", checkLogin, confirmUpload);
router.get("/upload-status", checkLogin, getVideoStatus);
router.get("/thumbnails", checkLogin, getVideoThumbnails);
router.get("/thumbnails/select", checkLogin, getSelectedThumbail);
router.post("/thumbnails/select", checkLogin, selectThumbnail);
router.post(
	"/thumbnails/custom/signed-url",
	checkLogin,
	getCustomThumbnailSignedUrl
);
router.post(
	"/thumbnails/custom/confirm",
	checkLogin,
	confirmCustomThumbnailUpload
);

export default router;
