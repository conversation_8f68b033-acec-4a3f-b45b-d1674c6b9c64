import {
	<PERSON><PERSON>,
	Container,
	<PERSON>ack,
	Textarea,
	TextInput,
	Title,
	Text,
	Card,
	Flex,
	Tabs,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import { DateTimePicker } from "@mantine/dates";
import type React from "react";
import apiClient from "../../../config/axios";
import { notifications } from "@mantine/notifications";
import { IconCheck, IconNotes, IconX } from "@tabler/icons-react";
import { isAxiosError } from "axios";
import { useCallback, useEffect, useRef, useState } from "react";
import { CalendarDays, FileText, Save } from "lucide-react";
import { useNavigate, useParams } from "react-router-dom";
import CustomQuill from "../../quill/CustomQuill";
import type { EventDetailsType, EventFormValues } from "../../../types";
import ImageUpload from "./ImageUpload";
import FullScreenLoader from "../../FullScreenLoader";

type EventFormProps = {
	initialValues?: Partial<EventFormValues>;
	onSubmit?: (values: EventFormValues) => void;
};

export const CreateEventForm: React.FC = (props: EventFormProps) => {
	const { eventId, eventName, activeTab } = useParams();

	const navigate = useNavigate();
	const fieldRefs = useRef<Record<string, HTMLElement | null>>({});

	const [images, setImages] = useState<
		(File | EventDetailsType["creationMedia"][0])[]
	>([]);
	const [thumbnailImage, setThumbnailImage] = useState<
		File | EventDetailsType["creationMedia"][0] | null
	>(null);

	// Track which existing media (from server) were originally present
	const initialExistingMediaIdsRef = useRef<string[]>([]);

	const [loading, setLoading] = useState<boolean>(false);

	useEffect(() => {
		if (!eventId && activeTab === "images") {
			navigate(`/events/event/${eventId}/info`);
		}
	}, [activeTab, eventId, navigate]);

	const setFieldRef = (field: string) => (el: HTMLElement | null) => {
		fieldRefs.current[field] = el;
	};

	const scrollToError = (field: string) => {
		const ref = fieldRefs.current[field];
		if (ref) {
			ref.scrollIntoView({ behavior: "smooth", block: "center" });
			if ("focus" in ref)
				(ref as HTMLElement).focus({ preventScroll: true });
		}
	};

	const form = useForm<EventFormValues>({
		initialValues: {
			name: props.initialValues?.name || "",
			description: props.initialValues?.description || "",
			startDate: props.initialValues?.startDate || null,
			endDate: props.initialValues?.endDate || null,
			detail: props.initialValues?.detail || "",
		},
		validate: {
			name: val => (val.trim() === "" ? "Name is required" : null),
			description: val =>
				val.trim() === "" ? "Description is required" : null,
			detail: val => (val.trim() === "" ? "Detail is required" : null),
			startDate: (value: Date | null, values: EventFormValues) => {
				if (!value) return "Start date is required";

				const dateValue =
					value instanceof Date ? value : new Date(value);

				const now = new Date();

				if (dateValue.getTime() < now.getTime()) {
					return "Start date cannot be in the past";
				}

				if (
					values.startDate &&
					values.endDate &&
					values.startDate >= values.endDate
				) {
					return "Start date must be before End Date";
				}

				return null;
			},
			endDate: (value: Date | null, values: EventFormValues) =>
				!value
					? "End date is required"
					: values.startDate && value < values.startDate
						? "End date cannot be before start date"
						: null,
		},
		transformValues: values => ({
			...values,
			name: values.name.trim(),
			description: values.description.trim(),
		}),
	});

	const handleCreateEvent = async (values: EventFormValues) => {
		const validation = form.validate();
		if (validation.hasErrors) {
			const firstError = Object.keys(validation.errors)[0];
			scrollToError(firstError);
			return;
		}

		setLoading(true);
		try {
			const response = await apiClient.post("/api/events/create-event", {
				...values,
			});

			notifications.show({
				title: "Success",
				message: "Event created successfully",
				color: "green",
				icon: <IconCheck />,
			});
			form.reset();
			navigate(
				`/events/update-event/${values.name}/${response.data.eventId}/images`
			);
		} catch (err) {
			if (isAxiosError(err)) {
				notifications.show({
					title: "Unable to create event",
					message:
						err.response?.data?.message || "Can't create event",
					color: "red",
					icon: <IconX />,
				});
			}
		} finally {
			setLoading(false);
		}
	};

	const handleUpdateEvent = async (values: EventFormValues) => {
		const validation = form.validate();
		if (validation.hasErrors) {
			const firstError = Object.keys(validation.errors)[0];
			scrollToError(firstError);
			return;
		}

		setLoading(true);
		try {
			const creationMedia: {
				isThumbnail: boolean;
				url: string;
				S3Key: string;
				key: string;
				type: string;
			}[] = [];

			// Compute which existing media were removed in the UI
			const existingNow = images.filter(
				(img): img is EventDetailsType["creationMedia"][0] =>
					!(img instanceof File)
			) as EventDetailsType["creationMedia"][0][];
			const currentExistingIdsSet = new Set(existingNow.map(m => m._id));
			const removedMediaIds = initialExistingMediaIdsRef.current.filter(
				id => !currentExistingIdsSet.has(id)
			);
			// If thumbnail is an existing image, pass its id so backend can set it
			let existingThumbnailId: string | undefined = undefined;
			if (thumbnailImage && !(thumbnailImage instanceof File)) {
				existingThumbnailId = (
					thumbnailImage as EventDetailsType["creationMedia"][0]
				)._id;
			}

			const isUploadSuccess = await handleUpload(creationMedia);
			if (!isUploadSuccess) {
				return;
			}
			await apiClient.put(`/api/events/update-event/${eventId}`, {
				...values,
				creationMedia,
				removedMediaIds,
				existingThumbnailId,
			});

			notifications.show({
				title: "Success",
				message: "Event updated successfully",
				color: "green",
				icon: <IconCheck />,
			});
		} catch (err) {
			if (isAxiosError(err)) {
				notifications.show({
					title: "Unable to update event",
					message:
						err.response?.data?.message || "Can't update event",
					color: "red",
					icon: <IconX />,
				});
			}
		} finally {
			setLoading(false);
		}
	};


	const handleUpload = async (
		creationMedia: {
			isThumbnail: boolean;
			url: string;
			S3Key: string;
			type: string;
		}[]
	): Promise<boolean> => {
		try {
			if (images.length === 0) {
				notifications.show({
					title: "Images Required",
					message: "Please upload at least one image.",
					color: "red",
					icon: <IconX />,
				});
				return false;
			}
			if (!thumbnailImage) {
				notifications.show({
					title: "Thumbnail Required",
					message: "Please select a thumbnail image.",
					color: "red",
					icon: <IconX />,
				});
				return false;
			}

			const newImages = images.filter(
				image => image instanceof File
			) as File[];

			for (const image of newImages) {
				const { data: signedUrlData } = await apiClient.get(
					`/api/events/signed-url/${eventId}?fileName=${image.name}&fileType=${image.type}`
				);

				await fetch(signedUrlData.uploadUrl, {
					method: "PUT",
					headers: {
						"Content-Type": image.type,
					},
					body: image,
				});

				const imageKey = getImageKey(image);

				creationMedia.push({
					isThumbnail: ,
					url: signedUrlData.url,
					S3Key: signedUrlData.S3Key,
					type: "image",
				});
			}
			return true;
		} catch (error) {
			console.log(error);
			if (isAxiosError(error)) {
				notifications.show({
					title: "Failed to upload images",
					message:
						error.response?.data?.message ||
						"Failed to upload images",
					color: "red",
					icon: <IconX />,
				});
			} else {
				notifications.show({
					title: "Failed to upload images",
					message: "Failed to upload images",
					color: "red",
					icon: <IconX />,
				});
			}
			return false;
		}
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		if (eventId) {
			handleUpdateEvent(form.values);
		} else {
			handleCreateEvent(form.values);
		}
	};

	const { setValues } = form;
	const fetchEvent = useCallback(async () => {
		try {
			const response = await apiClient.get<EventFormValues>(
				`/api/events/get-event/${eventId}`
			);

			const { creationMedia, ...rest } = response.data;

			setValues(rest);

			if (creationMedia) {
				setImages(creationMedia);
				// Remember which existing media were originally loaded
				initialExistingMediaIdsRef.current = creationMedia.map(
					m => m._id
				);
				const thumbnail = creationMedia.find(
					media => media.isThumbnail
				);
				if (thumbnail) {
					setThumbnailImage(thumbnail);
				}
			}
		} catch (error) {
			console.log(error);
		}
	}, [eventId, setValues]);

	const tabChangeHandler = (value: string) => {
		if (!eventId) return;
		if (value === activeTab) return;
		navigate(`/events/update-event/${eventName}/${eventId}/${value}`);
	};

	useEffect(() => {
		if (eventId) {
			fetchEvent();
		}
	}, [eventId, fetchEvent]);

	if (loading) return <FullScreenLoader />;

	return (
		<Container>
			<Stack mb="md" gap={0}>
				<Title>{eventId ? "Update Event" : " Create New Event"}</Title>
				{!eventId && (
					<Text c="dimmed">
						Fill out the form below to create a new event with
						images and scheduling details.
					</Text>
				)}
			</Stack>

			<form onSubmit={handleSubmit}>
				<Stack>
					<Tabs
						variant="pills"
						value={activeTab ?? "info"}
						onChange={e => tabChangeHandler(e?.valueOf() as string)}
					>
						<Tabs.List grow>
							{eventId && (
								<>
									<Tabs.Tab value="info">
										Information
									</Tabs.Tab>
									<Tabs.Tab value="images">Images</Tabs.Tab>
								</>
							)}
						</Tabs.List>
						<Tabs.Panel value="info">
							<Stack pt="lg">
								<Card shadow="xl" p="lg" withBorder>
									<Stack>
										<Flex align="center" gap="xs">
											<FileText size={20} />
											<Title order={3}>
												Basic Information
											</Title>
										</Flex>
										<TextInput
											ref={setFieldRef("name")}
											label="Event Name"
											placeholder="Enter event name"
											{...form.getInputProps("name")}
											withAsterisk
											error={
												form.errors["name"]
													? form.errors["name"]
													: undefined
											}
										/>

										<Textarea
											withAsterisk
											label="Description"
											ref={setFieldRef("description")}
											placeholder="Enter event description"
											{...form.getInputProps(
												"description"
											)}
											styles={{
												input: {
													height: "140px",
												},
												section: {
													display: "flex",
													alignItems: "flex-start",
													justifyContent: "center",
													marginTop: "0.45rem",
												},
											}}
											error={
												form.errors["description"]
													? form.errors["description"]
													: undefined
											}
										/>
									</Stack>
								</Card>

								<Card shadow="xl" p="lg" withBorder>
									<Stack>
										<Flex align="center" gap="xs">
											<IconNotes size={20} />
											<Title order={3}>
												Event Details
											</Title>
										</Flex>

										<div ref={setFieldRef("detail")}>
											<CustomQuill
												value={form.values.detail}
												onChange={val =>
													form.setFieldValue(
														"detail",
														val
													)
												}
												minHeight="280px"
											/>
											{form.errors.detail && (
												<Text c="red" size="xs" mt={3}>
													{form.errors.detail}
												</Text>
											)}
										</div>
									</Stack>
								</Card>

								<Card shadow="xl" p="lg" withBorder>
									<Stack>
										<Flex align="center" gap="xs">
											<CalendarDays size={20} />
											<Title order={3}>Schedule</Title>
										</Flex>
										<Flex
											align="center"
											justify="space-between"
											gap="md"
										>
											<DateTimePicker
												ref={setFieldRef("startDate")}
												style={{ flex: 1 }}
												label="Start Date and Time"
												placeholder="Pick start date"
												{...form.getInputProps(
													"startDate"
												)}
												withAsterisk
												hideOutsideDates={true}
												weekendDays={[]}
												timePickerProps={{
													withDropdown: true,
													format: "24h",
												}}
												minDate={new Date()}
												maxDate={
													form.values.endDate ??
													undefined
												}
											/>

											<DateTimePicker
												ref={setFieldRef("endDate")}
												style={{ flex: 1 }}
												label="End Date and Time"
												placeholder="Pick end date"
												{...form.getInputProps(
													"endDate"
												)}
												withAsterisk
												hideOutsideDates={true}
												weekendDays={[]}
												timePickerProps={{
													withDropdown: true,
													format: "24h",
												}}
												minDate={
													form.values.startDate ||
													new Date()
												}
											/>
										</Flex>
									</Stack>
								</Card>
							</Stack>
						</Tabs.Panel>
						{eventId && (
							<Tabs.Panel value="images">
								<Stack pt="lg">
									<ImageUpload
										images={images}
										setImages={setImages}
										thumbnailImage={thumbnailImage}
										setThumbnailImage={setThumbnailImage}
									/>
								</Stack>
							</Tabs.Panel>
						)}
					</Tabs>
					<Flex justify="flex-end">
						<Button
							size="sm"
							loading={loading}
							type="submit"
							leftSection={<Save size={16} />}
						>
							{eventId ? "Update" : "Create"} Event
						</Button>
					</Flex>
				</Stack>
			</form>
		</Container>
	);
};
