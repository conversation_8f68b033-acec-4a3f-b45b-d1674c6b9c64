import type { FileWithPath } from "@mantine/dropzone";
import type { ActionInfoType, profileStatusDataType } from "../types";

interface FallbackImageProps {
	firstName: string;
	lastName: string;
}

export function fallbackImage({
	firstName,
	lastName,
}: FallbackImageProps): string {
	if (
		!firstName ||
		!lastName ||
		/^\d/.test(firstName.charAt(0)) ||
		/^\d/.test(lastName.charAt(0))
	) {
		return `https://api.dicebear.com/5.x/initials/svg?seed=SM`;
	}

	return `https://api.dicebear.com/5.x/initials/svg?seed=${firstName.charAt(0)}${lastName.charAt(0)}`;
}

export function getProfileStatus(profileStatus: profileStatusDataType): string {
	switch (profileStatus) {
		case "onboarding":
			return "Onboarding";
		case "pending":
			return "Pending Review";
		case "re-approved":
			return "Awaiting Re-approval";
		case "approved":
			return "Approved";
		case "changes_requested":
			return "Changes Requested";
		default:
			return "Unknown";
	}
}

export function feedbackMessage(isRead?: boolean) {
	if (isRead === undefined) {
		return "No feedback provided";
	}
	if (isRead) {
		return "Seen by user";
	}
	return "User hasn’t seen feedback";
}

export function getStepperColors(index: number, actionInfo: ActionInfoType) {
	const defaultColor = "var(--mantine-color-blue-6)";
	const pendingColor = "var(--mantine-color-yellow-6)";
	const completedColor = "var(--mantine-color-green-6)";
	switch (index) {
		case 0:
			if (actionInfo.basicDetails === undefined) return defaultColor;
			return actionInfo.basicDetails ? completedColor : pendingColor;

		case 1:
		case 2:
			if (actionInfo.earlyLife === undefined) return defaultColor;
			return actionInfo.earlyLife ? completedColor : pendingColor;

		case 3:
		case 4:
			if (actionInfo.professionalLife === undefined) return defaultColor;
			return actionInfo.professionalLife ? completedColor : pendingColor;

		case 5:
		case 6:
			if (actionInfo.currentLife === undefined) return defaultColor;
			return actionInfo.currentLife ? completedColor : pendingColor;

		default:
			return defaultColor;
	}
}