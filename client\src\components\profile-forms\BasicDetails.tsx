import {
	Card,
	Text,
	Group,
	Stack,
	Box,
	Image,
	TextInput,
	SimpleGrid,
	Flex,
	ActionIcon,
	Tooltip,
	Button,
} from "@mantine/core";
import {
	IconMail,
	IconBrandX,
	IconBrandInstagram,
	IconBrandLinkedin,
	IconHome,
	IconHash,
	IconBuilding,
	IconId,
	IconLink,
	IconTrash,
	IconPlus,
} from "@tabler/icons-react";
import React from "react";
import type { UserProfile } from "../../types";
import { MapPin, Phone, Quote, Smile, Upload, User } from "lucide-react";
import type { UseFormReturnType } from "@mantine/form";
import { fallbackImage } from "../../utils";

type UserProfileCardsProps = {
	form: UseFormReturnType<UserProfile>;
	imageSrc: string;
	handleImageChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
	imageRef: React.RefObject<HTMLImageElement | null>;
	fileRef: React.RefObject<HTMLInputElement | null>;
	renderField: (
		label: string,
		field: keyof UserProfile,
		icon?: React.ReactNode,
		disabled?: boolean,
		options?: { textarea?: boolean; placeholder?: string },
		isRequired?: boolean
	) => React.ReactNode;
};

const BasicDetails = (props: UserProfileCardsProps) => {
	const {
		form,
		imageSrc,
		imageRef,
		fileRef,
		handleImageChange,
		renderField,
	} = props;

	return (
		<>
			<Stack gap="md">
				<Card withBorder shadow="sm" radius="md">
					<Stack gap="md">
						<Group>
							<Text fw={600}>Basic Information</Text>
						</Group>
						<Flex gap={50} align={"center"} justify={"center"}>
							<Box>
								<Text size="sm" ml={4}>
									Profile Picture
									<Text component="span" c="red" pl={4}>
										*
									</Text>
								</Text>

								<div className="relative w-[128px] h-[128px] mx-auto group rounded-[var(--mantine-radius-xl)] overflow-hidden">
									<Image
										ref={imageRef}
										src={imageSrc}
										alt="Profile"
										h={128}
										w={128}
										fallbackSrc={fallbackImage({
											firstName: form.values.firstName,
											lastName: form.values.secondName,
										})}
										radius="xl"
										style={{
											border: "4px solid #dbeafe",
										}}
									/>

									<div
										onClick={() => fileRef.current?.click()}
										className="absolute inset-0 bg-black/40 text-white flex items-center justify-center opacity-0 group-hover:opacity-100 cursor-pointer transition duration-200"
									>
										<Upload size={24} />
									</div>
								</div>

								<input
									ref={fileRef}
									type="file"
									accept="image/*"
									onChange={handleImageChange}
									className="hidden"
									required
								/>

								{form.errors.image && (
									<Text c="red" size="sm" mt={4}>
										{form.errors.image}
									</Text>
								)}
							</Box>

							<SimpleGrid cols={2} spacing="lg" w={"100%"}>
								{renderField(
									"First Name",
									"firstName",
									<User size={16} />,
									false,
									{ placeholder: "e.g.,  John" },
									true
								)}

								{renderField(
									"Middle Name",
									"middleName",
									<User size={16} />,
									false,
									{ placeholder: "e.g.,  Fitzgerald" }
								)}

								{renderField(
									"Last Name",
									"secondName",
									<User size={16} />,
									false,
									{ placeholder: "e.g.,  Kennedy" },
									true
								)}

								{renderField(
									"Organization",
									"currentOrganization",
									<IconBuilding size={16} />,
									false,
									undefined,
									true
								)}
							</SimpleGrid>
						</Flex>

						{renderField(
							"A quote that inspires me",
							"quote",
							<Quote size={16} />,
							false,
							{
								textarea: true,
								placeholder:
									"This quote will be displayed as a part of your profile. Please provide the full quote along with the source.",
							},
							true
						)}
					</Stack>
				</Card>
				<Stack gap="md">
					<Card withBorder shadow="sm" radius="md">
						<Stack gap="md">
							<Group>
								<Text fw={600}>About</Text>
							</Group>
							{renderField(
								"Introduction",
								"introduction",
								<IconId size={16} />,
								false,
								{
									textarea: true,
									placeholder:
										"Your introduction in 4-5 sentences or 100 words.",
								},
								true
							)}
							{renderField(
								"Current City",
								"city",
								<MapPin size={16} />,
								false,
								{
									placeholder: "e.g., Bangalore, India",
								},
								true
							)}
							{renderField(
								"Pincode",
								"pincode",
								<IconHash size={16} />,
								false,
								{ placeholder: "e.g., 560001" },
								true
							)}
							{renderField(
								"Locality / Area",
								"address",
								<IconHome size={16} />,
								false,
								{
									placeholder:
										"e.g., Whitefield/Kormangala/Sector 23 Dwarka",
								}
							)}
							{renderField(
								"What fills you with joy, outside your work?",
								"joy",
								<Smile size={16} />,
								false,
								{
									textarea: true,
									placeholder:
										"Keep this short and concise not more than 2 lines.",
								},
								true
							)}
						</Stack>
					</Card>

					<Card withBorder shadow="sm" radius="md">
						<Stack gap="md">
							<Group>
								<Text fw={600}>Contact & Social</Text>
							</Group>
							{renderField(
								"Email",
								"email",
								<IconMail size={16} />,
								true,
								{
									placeholder: "e.g.,  <EMAIL>",
								},
								true
							)}
							{renderField(
								"Mobile",
								"mobile",
								<Phone size={16} />,
								true,
								{ placeholder: "e.g.,  +919560088456" },
								true
							)}
							{renderField(
								"Twitter",
								"twitter",
								<IconBrandX size={16} />,
								false,
								{
									placeholder:
										"e.g.,  https://x.com/username",
								}
							)}
							{renderField(
								"Instagram",
								"instagram",
								<IconBrandInstagram size={16} />,
								false,
								{
									placeholder:
										"e.g.,  https://instagram.com/username",
								}
							)}
							{renderField(
								"LinkedIn",
								"linkedIn",
								<IconBrandLinkedin size={16} />,
								false,
								{
									placeholder:
										"e.g.,  https://linkedin.com/in/username",
								}
							)}
						</Stack>
					</Card>

					<Card withBorder shadow="sm" radius="md">
						<Stack gap="md">
							<Group>
								<Text fw={600}>Content Links</Text>
							</Group>
							{form.values.contentLinks &&
								form.values.contentLinks.length > 0 &&
								form.values.contentLinks.map((_link, index) => (
									<Group key={index} gap="md" align="center">
										<TextInput
											leftSection={<IconLink size={16} />}
											placeholder="e.g., https://blog.com/post"
											size="sm"
											radius="md"
											className="flex-1"
											{...form.getInputProps(
												`contentLinks.${index}`
											)}
										/>
										<Tooltip label="Remove link">
											<ActionIcon
												color="red"
												variant="subtle"
												onClick={() =>
													form.removeListItem(
														"contentLinks",
														index
													)
												}
												size="md"
												radius="md"
											>
												<IconTrash size={18} />
											</ActionIcon>
										</Tooltip>
									</Group>
								))}
							{form.errors.contentLinks && (
								<Text c="red" size="sm" mt={4}>
									{form.errors.contentLinks}
								</Text>
							)}

							<Button
								variant="dashed"
								onClick={() =>
									form.insertListItem("contentLinks", "")
								}
								size="sm"
								radius="md"
								leftSection={<IconPlus size={16} />}
							>
								Add Content Link
							</Button>
						</Stack>
					</Card>

					<Card withBorder shadow="sm" radius="md">
						<Stack gap="md">
							<Group>
								<Text fw={600}>Other Social Handles</Text>
							</Group>
							{form.values.otherSocialHandles &&
								form.values.otherSocialHandles.length > 0 &&
								form.values.otherSocialHandles.map(
									(_, index) => (
										<Group
											key={index}
											gap="md"
											align="center"
										>
											<TextInput
												leftSection={
													<IconLink size={16} />
												}
												placeholder="e.g., https://example.com/username"
												size="sm"
												radius="md"
												className="flex-1"
												{...form.getInputProps(
													`otherSocialHandles.${index}`
												)}
											/>
											<Tooltip label="Remove link">
												<ActionIcon
													color="red"
													variant="subtle"
													onClick={() =>
														form.removeListItem(
															"otherSocialHandles",
															index
														)
													}
													size="md"
													radius="md"
												>
													<IconTrash size={18} />
												</ActionIcon>
											</Tooltip>
										</Group>
									)
								)}
							{form.errors.otherSocialHandles && (
								<Text c="red" size="sm" mt={4}>
									{form.errors.otherSocialHandles}
								</Text>
							)}
							<Button
								variant="dashed"
								onClick={() => {
									form.insertListItem(
										"otherSocialHandles",
										""
									);
								}}
								w={"100%"}
								size="sm"
								radius="md"
								leftSection={<IconPlus size={16} />}
							>
								Add Social Handles
							</Button>
						</Stack>
					</Card>
				</Stack>
			</Stack>
		</>
	);
};

export default BasicDetails;
