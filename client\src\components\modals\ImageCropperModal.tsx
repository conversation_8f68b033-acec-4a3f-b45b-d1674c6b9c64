import { useState, useCallback, useEffect } from "react";
import <PERSON><PERSON><PERSON>, { type Area } from "react-easy-crop";
import { Modal, Button, Stack, Box, Text, Group, Title } from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { IconCheck, IconX } from "@tabler/icons-react";

// cropUtils.ts
async function getCroppedImg(
	imageSrc: string,
	pixelCrop: Area
): Promise<string> {
	const image = await createImage(imageSrc);
	const canvas = document.createElement("canvas");
	const ctx = canvas.getContext("2d");

	if (!ctx) throw new Error("No 2d context"); // Final size

	canvas.width = pixelCrop.width;
	canvas.height = pixelCrop.height;

	ctx.drawImage(
		image,
		pixelCrop.x,
		pixelCrop.y,
		pixelCrop.width,
		pixelCrop.height,
		0,
		0,
		pixelCrop.width,
		pixelCrop.height
	);
	return canvas.toDataURL("image/png");
}

function createImage(url: string): Promise<HTMLImageElement> {
	return new Promise((resolve, reject) => {
		const img = new Image();
		img.addEventListener("load", () => resolve(img));
		img.addEventListener("error", error => reject(error));
		img.setAttribute("crossOrigin", "anonymous");
		img.src = url;
	});
}

// Convert base64 data URL to Blob
function dataURLtoBlob(dataUrl: string) {
	const arr = dataUrl.split(",");
	const mime = arr[0].match(/:(.*?);/)?.[1] || "image/png";
	const bstr = atob(arr[1]);
	let n = bstr.length;
	const u8arr = new Uint8Array(n);
	while (n--) {
		u8arr[n] = bstr.charCodeAt(n);
	}
	return new Blob([u8arr], { type: mime });
}

type ImageCropperModalProps = {
	isOpen: boolean;
	fileToCrop: File | null;
	onCropDone: (croppedFile: File) => void;
	onCancel: () => void;
};

const ImageCropperModal = ({
	isOpen,
	fileToCrop,
	onCropDone,
	onCancel,
}: ImageCropperModalProps) => {
	const [crop, setCrop] = useState({ x: 0, y: 0 });
	const [zoom, setZoom] = useState(1);
	const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(
		null
	);
	const [isProcessing, setIsProcessing] = useState(false);

	const onCropComplete = useCallback((_: Area, croppedPixels: Area) => {
		setCroppedAreaPixels(croppedPixels);
	}, []);

	const imageUrl = fileToCrop ? URL.createObjectURL(fileToCrop) : null;

	useEffect(() => {
		return () => {
			if (imageUrl) {
				URL.revokeObjectURL(imageUrl);
			}
		};
	}, [imageUrl]);

	const handleCropAndSave = async () => {
		if (!fileToCrop || !croppedAreaPixels) {
			notifications.show({
				title: "Error",
				message: "Please select an area to crop.",
				color: "red",
				icon: <IconX size={16} />,
			});
			return;
		}

		setIsProcessing(true);
		try {
			const base64DataUrl = await getCroppedImg(
				imageUrl as string,
				croppedAreaPixels
			);

			const blob = dataURLtoBlob(base64DataUrl);

			const croppedFile = new File(
				[blob],
				`${fileToCrop.name.replace(/\.[^/.]+$/, "")}.png`,
				{ type: blob.type }
			);

			onCropDone(croppedFile);

			notifications.show({
				title: "Crop Success",
				message: "Image cropped successfully.",
				color: "green",
				icon: <IconCheck size={16} />,
			});
		} catch (err) {
			console.error(err);
			notifications.show({
				title: "Crop Failed",
				message: "Failed to crop image. Please try again.",
				color: "red",
				icon: <IconX size={16} />,
			});
		} finally {
			setIsProcessing(false);
			onCancel();
		}
	};

	return (
		<Modal
			opened={isOpen && !!fileToCrop}
			onClose={onCancel}
			title={
				<Stack gap={0}>
					<Title order={4}>Select Profile Picture Area</Title>
					<Text size="sm" c="dimmed">
						Drag and zoom to select your profile picture area.
					</Text>
				</Stack>
			}
			size="xl"
			centered
			closeOnClickOutside={!isProcessing}
			closeOnEscape={!isProcessing}
			trapFocus={false}
			withCloseButton={false}
		>
			<Stack gap="md">
				<Box
					style={{
						position: "relative",
						width: "100%",
						height: 400,
						background: "#f8f9fa",
					}}
				>
					{imageUrl && (
						<Cropper
							image={imageUrl}
							crop={crop}
							zoom={zoom}
							aspect={1}
							cropShape="rect" // Rectangular crop
							onCropChange={setCrop}
							onZoomChange={setZoom}
							onCropComplete={onCropComplete}
						/>
					)}
				</Box>

				<Group justify="flex-end" mt="sm">
					<Button
						variant="outline"
						onClick={onCancel}
						disabled={isProcessing}
					>
						Cancel
					</Button>
					<Button
						onClick={handleCropAndSave}
						loading={isProcessing}
						disabled={isProcessing || !croppedAreaPixels}
					>
						Crop & Save
					</Button>
				</Group>
			</Stack>
		</Modal>
	);
};

export default ImageCropperModal;
