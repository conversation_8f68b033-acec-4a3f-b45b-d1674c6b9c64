import { <PERSON><PERSON>, Tabs, Group, Badge, Text } from "@mantine/core";
import Profile from "../pages/Profile";
import EarlyLifePreview from "./profile/EarlyLifePreview";
import ProfessionalLifePreview from "./profile/ProfessionalLifePreview";
import CurrentLifePreview from "./profile/CurrentLifePreview";
import { useEffect, useMemo, useState } from "react";
import EarlyLifeForm from "./profile-forms/EarlyLifeForm";
import { Edit } from "lucide-react";
import ProfessionalLifeForm from "./profile-forms/ProfessionalLifeForm";
import CurrentLifeForm from "./profile-forms/CurrentLifeForm";
import { useAuth } from "../contexts/AuthContext";
import FullScreenLoader from "./FullScreenLoader";
import { profileStatusColorMap, tabsMap, tabsMapReverse } from "../constants";
import type { tabsDataType } from "../types";
import { useNavigate, useParams } from "react-router-dom";
import { getProfileStatus } from "../utils";
import {
	IconBriefcase,
	IconClock,
	IconHome,
	IconUser,
} from "@tabler/icons-react";

type ProfileTabsProps = {
	stickyTop?: number | string;
	noEditInProfile?: boolean;
};

const ProfileTabs = ({
	stickyTop = "90px",
	noEditInProfile,
}: ProfileTabsProps) => {
	const [editingTab, setEditingTab] = useState<string | null>(null);
	const { user } = useAuth();
	const { tab, step } = useParams();
	const navigate = useNavigate();

	const handleSetEditing = (tab: string | null) => {
		setEditingTab(tab);
	};

	const tabChangeHandler = (newTab: tabsDataType) => {
		if (step) {
			navigate(`/finalStep/${tabsMapReverse[newTab]}`);
			return;
		}
		if (newTab === tab) return;
		navigate(`/profile/${tabsMapReverse[newTab]}`);
	};

	useEffect(() => {
		const activeTab =
			tabsMap[tab as keyof typeof tabsMap] ?? "basic-details";
		if (step) {
			navigate(
				`/finalStep/${tabsMapReverse[activeTab as tabsDataType]}`,
				{ replace: true }
			);
			return;
		}
		navigate(`/profile/${tabsMapReverse[activeTab as tabsDataType]}`, {
			replace: true,
		});
	}, []);

	const isReApproved: boolean = useMemo(() => {
		if (user?.profileStatus === "re-approved") {
			return true;
		} else {
			return false;
		}
	}, [user?.profileStatus]);

	const canViewLifeTabs = useMemo(() => {
		if (!user) return false;
		if (user.onboardingStepCompleted) {
			return true;
		}
		if (user?.isAdminPanelUser) {
			return false;
		}
		return true;
	}, [user]);

	if (!user) return <FullScreenLoader />;

	return (
		<div style={{ minHeight: "calc(100vh - 250px)" }}>
			{isReApproved && (
				<Group justify="space-between" mt={20} mb={10}>
					<Text size="sm" c="red">
						Your changes are currently visible only to you and
						admins. Once approved, they will be visible to everyone.
					</Text>
					<Badge
						color={profileStatusColorMap["re-approved"]}
						size="md"
						radius="sm"
						style={{
							textTransform: "capitalize",
						}}
					>
						{getProfileStatus("re-approved")}
					</Badge>
				</Group>
			)}
			<Tabs
				value={tabsMap[tab as keyof typeof tabsMap] ?? "basic-details"}
				onChange={e => tabChangeHandler(e?.valueOf() as tabsDataType)}
			>
				<Tabs.List
					style={{
						position: "sticky",
						top: stickyTop,
						zIndex: 10,
						height: "40px",
						backgroundColor: "white",
					}}
				>
					<Tabs.Tab
						value="basic-details"
						leftSection={<IconUser size={16} />}
					>
						Basic Details
					</Tabs.Tab>

					{canViewLifeTabs && (
						<>
							<Tabs.Tab
								value="early-life"
								leftSection={<IconClock size={16} />}
							>
								Early Life Details
							</Tabs.Tab>
							<Tabs.Tab
								value="professional-life"
								leftSection={<IconBriefcase size={16} />}
							>
								Professional Life Details
							</Tabs.Tab>
							<Tabs.Tab
								value="current-life"
								leftSection={<IconHome size={16} />}
							>
								Current Life Details
							</Tabs.Tab>
						</>
					)}
				</Tabs.List>

				<Tabs.Panel value="basic-details" style={{ padding: "0rem" }}>
					<Profile noEditInProfile={noEditInProfile} />
				</Tabs.Panel>

				{canViewLifeTabs && (
					<>
						<Tabs.Panel
							value="early-life"
							style={{
								padding:
									editingTab === "early-life"
										? "2rem 1rem 1rem 1rem"
										: "0rem",
							}}
						>
							{editingTab !== "early-life" ? (
								<>
									{!noEditInProfile && (
										<Group justify="flex-end">
											<Button
												onClick={() =>
													handleSetEditing(
														"early-life"
													)
												}
												m="lg"
												mt={"md"}
												leftSection={<Edit size={16} />}
											>
												Edit
											</Button>
										</Group>
									)}
									<EarlyLifePreview />
								</>
							) : (
								<EarlyLifeForm
									lifeData={null}
									editing={editingTab === "early-life"}
									setEditing={isEditing =>
										handleSetEditing(
											isEditing ? "early-life" : null
										)
									}
									userId={user._id}
									onboardingStepCompleted={
										user.onboardingStepCompleted
									}
									isEditable={editingTab === "early-life"}
									onFormSuccess={() => handleSetEditing(null)}
								/>
							)}
						</Tabs.Panel>

						<Tabs.Panel
							value="professional-life"
							style={{
								padding:
									editingTab === "professional-life"
										? "2rem 1rem 1rem 1rem"
										: "0rem",
							}}
						>
							{editingTab !== "professional-life" ? (
								<>
									{!noEditInProfile && (
										<Group justify="flex-end">
											<Button
												m="lg"
												mt={"md"}
												onClick={() =>
													handleSetEditing(
														"professional-life"
													)
												}
												leftSection={<Edit size={16} />}
											>
												Edit
											</Button>
										</Group>
									)}
									<ProfessionalLifePreview />
								</>
							) : (
								<ProfessionalLifeForm
									lifeData={null}
									editing={editingTab === "professional-life"}
									setEditing={isEditing =>
										handleSetEditing(
											isEditing
												? "professional-life"
												: null
										)
									}
									isEditable={
										editingTab === "professional-life"
									}
									onFormSuccess={() => handleSetEditing(null)}
								/>
							)}
						</Tabs.Panel>

						<Tabs.Panel
							value="current-life"
							style={{
								padding:
									editingTab === "current-life"
										? "2rem 1rem 1rem 1rem"
										: "0rem",
							}}
						>
							{editingTab !== "current-life" ? (
								<>
									{!noEditInProfile && (
										<Group justify="flex-end">
											<Button
												m="lg"
												mt={"md"}
												onClick={() =>
													handleSetEditing(
														"current-life"
													)
												}
												leftSection={<Edit size={16} />}
											>
												Edit
											</Button>
										</Group>
									)}
									<CurrentLifePreview />
								</>
							) : (
								<CurrentLifeForm
									lifeData={null}
									editing={editingTab === "current-life"}
									setEditing={isEditing =>
										handleSetEditing(
											isEditing ? "current-life" : null
										)
									}
									isEditable={editingTab === "current-life"}
									onFormSuccess={() => handleSetEditing(null)}
								/>
							)}
						</Tabs.Panel>
					</>
				)}
			</Tabs>
		</div>
	);
};

export default ProfileTabs;
